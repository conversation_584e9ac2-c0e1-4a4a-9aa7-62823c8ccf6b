# OKX交易系统使用说明

## 📁 文件结构
```
📂 OKX交易系统/
├── 🎯 OKX主控制台.py          # 统一主控制台 (推荐使用)
├── 🔧 okx_trading_system_optimized.py  # 核心交易系统
└── 📖 使用说明.md             # 本文件
```

## 🚀 快速启动

### 自动模式 (推荐)
```bash
python OKX主控制台.py
```
- 🤖 **智能模式选择**: 自动检测账户余额
- 💰 **有余额**: 自动启动实盘交易
- 📊 **无余额**: 自动启动分析模式

### 指定模式启动
```bash
# 强制实盘交易模式
python OKX主控制台.py trading

# 强制分析模式  
python OKX主控制台.py analysis

# 手动菜单模式
python OKX主控制台.py menu
```

## 📊 功能说明

### 🎯 实盘交易模式
- ✅ 自动扫描37个活跃币种
- ✅ 多指标金叉信号检测 (EMA、KDJ、RSI、MA50、OBV)
- ✅ OBV 5/18金叉做多，死叉做空
- ✅ 智能风险控制和仓位管理
- ✅ 实时止损和动态平仓

### 📈 分析模式 (自动循环)
1. **实时走势监控** - TOP20币种实时价格、多时间周期涨跌幅
2. **动态排行榜** - 智能评分排序、综合推荐度
3. **多时间周期分析** - 1M/3M/5M/15M涨跌幅对比
4. **市场趋势统计** - 整体市场趋势分布

## ⚙️ 交易参数

### 风险控制
- 单笔风险: 2.5%
- 止损比例: 2.0%
- 最大日亏损: 5.0%
- 最大持仓数: 3个

### 信号阈值
- 买入阈值: 85%置信度
- 高质量信号: 96%置信度
- 扫描间隔: 5秒

## 🔍 数据显示

### 涨跌幅图标
- 🚀 强势上涨 (+5%以上)
- 📈 温和上涨 (+2%~+5%)
- 🟢 微涨 (0%~+2%)
- 🔴 微跌 (0%~-2%)
- 📉 温和下跌 (-2%~-5%)
- 💥 强势下跌 (-5%以下)

### 趋势分析
- 🚀 强势 (3-4个时间周期上涨)
- 📈 上涨 (2个时间周期上涨)
- 📉 下跌 (1个时间周期上涨)
- ➡️ 震荡 (平衡状态)

### 成交量等级
- 🔥 高量 (1000万以上)
- 🟡 中量 (100万-1000万)
- 🔵 低量 (100万以下)

## 🚨 安全提示

### 实盘交易风险
- ⚠️ 实盘交易有风险，请谨慎操作
- 💡 建议先用小资金测试
- 🛡️ 系统内置多重风险控制
- 🔄 随时可按 Ctrl+C 停止

### API配置
- 🔑 确保API密钥权限正确
- 🌐 检查网络连接稳定性
- 💰 确认账户余额充足

## 📱 操作说明

### 启动程序
1. 打开命令行/终端
2. 进入程序目录
3. 运行: `python OKX主控制台.py`

### 停止程序
- 按 `Ctrl + C` 安全退出
- 程序会自动保存交易记录

### 查看日志
- 交易日志自动保存
- 实时显示重要信息
- 错误信息会详细记录

## 🔧 故障排除

### 常见问题
1. **无法获取数据**
   - 检查网络连接
   - 验证API密钥

2. **账户余额显示0**
   - 检查API权限
   - 确认账户有USDT

3. **信号分析失败**
   - 网络延迟导致
   - 程序会自动重试

### 联系支持
- 📧 查看错误日志
- 🔍 检查系统状态
- 🛠️ 重启程序尝试

## 📈 性能优化

### 系统要求
- Python 3.7+
- 稳定网络连接
- 充足内存空间

### 最佳实践
- 🕐 避免网络高峰期
- 💾 定期备份配置
- 📊 监控系统性能

---

**版本**: v2.0  
**更新**: 2025-08-03  
**状态**: 生产就绪 ✅
