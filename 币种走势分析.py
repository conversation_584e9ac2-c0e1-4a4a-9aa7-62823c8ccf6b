#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OKX币种走势分析工具 - 统一版本
集成：动态排行榜、多时间周期分析、数据准确性检查、走势监控
"""

import time
import os
import sys
from datetime import datetime
from okx_trading_system_optimized import OKXOptimizedTradingSystem

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_main_menu():
    """显示主菜单"""
    clear_screen()
    print("🚀" * 50)
    print("                OKX币种走势分析工具")
    print("🚀" * 50)
    print()
    print("请选择功能:")
    print("1. 📊 实时走势监控 (推荐)")
    print("2. 🏆 动态排行榜")
    print("3. ⏱️ 多时间周期分析")
    print("4. 🔍 数据准确性检查")
    print("5. 📈 走势趋势分析")
    print("0. 退出")
    print()

def format_change_display(change_pct):
    """格式化涨跌幅显示"""
    if change_pct > 5:
        return f"🚀 +{change_pct:.2f}%"
    elif change_pct > 2:
        return f"📈 +{change_pct:.2f}%"
    elif change_pct > 0:
        return f"🟢 +{change_pct:.2f}%"
    elif change_pct > -2:
        return f"🔴 {change_pct:.2f}%"
    elif change_pct > -5:
        return f"📉 {change_pct:.2f}%"
    else:
        return f"💥 {change_pct:.2f}%"

def real_time_trend_monitor(system):
    """实时走势监控 - 主要功能"""
    print("🔄 启动实时走势监控...")
    
    while True:
        try:
            clear_screen()
            print("📊 实时走势监控")
            print("=" * 120)
            print(f"⏰ 更新时间: {datetime.now().strftime('%H:%M:%S')}")
            print()
            
            # 获取币种和排行榜数据
            symbols, ranking_data = system.get_dynamic_symbol_ranking()
            
            if not symbols:
                print("❌ 无法获取数据，3秒后重试...")
                time.sleep(3)
                continue
            
            print(f"📈 分析 {len(symbols)} 个活跃币种走势")
            print("=" * 120)
            print("排名 | 币种        | 当前价格      | 24h涨跌    | 1M    | 3M    | 5M    | 15M   | 成交量    | 趋势分析")
            print("-" * 120)
            
            # 分析前20个币种
            for i, symbol in enumerate(symbols[:20], 1):
                try:
                    # 获取实时数据
                    price_data = system.get_real_time_price(symbol)
                    if not price_data:
                        continue
                    
                    symbol_short = symbol.replace('-USDT-SWAP', '')
                    current_price = price_data['last_price']
                    change_24h = price_data['change_24h']
                    vol_24h = price_data['vol_24h'] / 10000  # 转换为万
                    
                    # 多时间周期数据
                    multi_changes = price_data.get('multi_timeframe_changes', {})
                    
                    # 格式化显示
                    change_24h_display = format_change_display(change_24h)
                    
                    # 短期涨跌幅
                    change_1m = multi_changes.get('1m', {}).get('change_pct', 0)
                    change_3m = multi_changes.get('3m', {}).get('change_pct', 0)
                    change_5m = multi_changes.get('5m', {}).get('change_pct', 0)
                    change_15m = multi_changes.get('15m', {}).get('change_pct', 0)
                    
                    # 简化显示短期涨跌
                    def short_format(pct):
                        if pct > 1:
                            return f"🟢{pct:+.1f}"
                        elif pct < -1:
                            return f"🔴{pct:.1f}"
                        else:
                            return f"⚪{pct:+.1f}"
                    
                    change_1m_s = short_format(change_1m)
                    change_3m_s = short_format(change_3m)
                    change_5m_s = short_format(change_5m)
                    change_15m_s = short_format(change_15m)
                    
                    # 趋势分析
                    positive_count = sum(1 for c in [change_1m, change_3m, change_5m, change_15m] if c > 0)
                    if positive_count >= 3:
                        trend = "🚀 强势"
                    elif positive_count >= 2:
                        trend = "📈 上涨"
                    elif positive_count <= 1:
                        trend = "📉 下跌"
                    else:
                        trend = "➡️ 震荡"
                    
                    # 成交量等级
                    if vol_24h > 10000:
                        vol_display = f"🔥{vol_24h:.0f}万"
                    elif vol_24h > 1000:
                        vol_display = f"🟡{vol_24h:.0f}万"
                    else:
                        vol_display = f"🔵{vol_24h:.0f}万"
                    
                    print(f"{i:>3}  | {symbol_short:<10} | {current_price:>12.6f} | {change_24h_display:<10} | {change_1m_s:<5} | {change_3m_s:<5} | {change_5m_s:<5} | {change_15m_s:<5} | {vol_display:<9} | {trend}")
                    
                except Exception as e:
                    continue
            
            print("-" * 120)
            print("📊 说明: 🟢上涨 🔴下跌 ⚪平稳 | 🚀强势 📈上涨 📉下跌 ➡️震荡")
            print("⏰ 10秒后自动刷新 | 按 Ctrl+C 返回菜单")
            
            time.sleep(10)
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(3)

def dynamic_ranking_board(system):
    """动态排行榜"""
    print("🔄 获取动态排行榜...")
    
    symbols, ranking_data = system.get_dynamic_symbol_ranking()
    
    if not ranking_data:
        print("❌ 无法获取排行榜数据")
        return
    
    clear_screen()
    print("🏆 币种动态排行榜")
    print("=" * 100)
    print("排名 | 币种名称      | 当前价格      | 24h涨跌    | 成交量(万)  | 综合评分 | 推荐度")
    print("-" * 100)
    
    for i, ticker in enumerate(ranking_data[:25], 1):
        symbol = ticker['instId'].replace('-USDT-SWAP', '')
        price = float(ticker['last'])
        change_24h = float(ticker.get('chg24h', ticker.get('changePercent24h', ticker.get('priceChangePercent', 0))))
        vol_24h = float(ticker['vol24h']) / 10000
        score = ticker['composite_score']
        
        change_display = format_change_display(change_24h)
        
        # 推荐度
        if score >= 80:
            recommend = "⭐⭐⭐⭐⭐"
        elif score >= 60:
            recommend = "⭐⭐⭐⭐"
        elif score >= 40:
            recommend = "⭐⭐⭐"
        else:
            recommend = "⭐⭐"
        
        print(f"{i:>3}  | {symbol:<12} | {price:>12.6f} | {change_display:<10} | {vol_24h:>9.0f} | {score:>7.1f} | {recommend}")
    
    print("-" * 100)
    input("\n按回车键返回菜单...")

def multi_timeframe_analysis(system):
    """多时间周期分析"""
    print("🔄 获取多时间周期数据...")
    
    symbols, _ = system.get_dynamic_symbol_ranking()
    
    if not symbols:
        print("❌ 无法获取币种数据")
        return
    
    clear_screen()
    print("⏱️ 多时间周期涨跌幅分析")
    print("=" * 110)
    print("币种名称      | 当前价格      | 24h涨跌    | 1分钟     | 3分钟     | 5分钟     | 15分钟    | 趋势")
    print("-" * 110)
    
    for symbol in symbols[:20]:
        try:
            price_data = system.get_real_time_price(symbol)
            if not price_data:
                continue
            
            symbol_short = symbol.replace('-USDT-SWAP', '')
            current_price = price_data['last_price']
            change_24h = price_data['change_24h']
            multi_changes = price_data.get('multi_timeframe_changes', {})
            
            change_24h_display = format_change_display(change_24h)
            
            # 各时间周期
            change_1m = format_change_display(multi_changes.get('1m', {}).get('change_pct', 0))
            change_3m = format_change_display(multi_changes.get('3m', {}).get('change_pct', 0))
            change_5m = format_change_display(multi_changes.get('5m', {}).get('change_pct', 0))
            change_15m = format_change_display(multi_changes.get('15m', {}).get('change_pct', 0))
            
            # 趋势判断
            changes = [multi_changes.get(tf, {}).get('change_pct', 0) for tf in ['1m', '3m', '5m', '15m']]
            positive_count = sum(1 for c in changes if c > 0)
            
            if positive_count >= 3:
                trend = "🚀 强势上涨"
            elif positive_count >= 2:
                trend = "📈 温和上涨"
            elif positive_count <= 1:
                trend = "📉 下跌趋势"
            else:
                trend = "➡️ 震荡整理"
            
            print(f"{symbol_short:<12} | {current_price:>12.6f} | {change_24h_display:<10} | {change_1m:<9} | {change_3m:<9} | {change_5m:<9} | {change_15m:<9} | {trend}")
            
        except Exception as e:
            continue
    
    print("-" * 110)
    input("\n按回车键返回菜单...")

def data_accuracy_check(system):
    """数据准确性检查"""
    print("🔍 执行数据准确性检查...")
    
    # 简化版检查
    symbols, _ = system.get_dynamic_symbol_ranking()
    
    if not symbols:
        print("❌ 币种列表获取失败")
        return
    
    print(f"✅ 币种列表: {len(symbols)} 个")
    
    # 测试前5个币种的数据质量
    success_count = 0
    test_symbols = symbols[:5]
    
    for symbol in test_symbols:
        try:
            # 测试K线数据
            klines = system.get_klines(symbol, '1m', 10)
            if len(klines) >= 8:
                success_count += 1
                print(f"✅ {symbol}: K线数据正常 ({len(klines)}条)")
            else:
                print(f"⚠️ {symbol}: K线数据不足 ({len(klines)}条)")
            
            # 测试实时价格
            price_data = system.get_real_time_price(symbol)
            if price_data and price_data['last_price'] > 0:
                print(f"✅ {symbol}: 实时价格正常 ({price_data['last_price']:.6f})")
            else:
                print(f"❌ {symbol}: 实时价格异常")
                
        except Exception as e:
            print(f"❌ {symbol}: 检查失败 - {e}")
    
    accuracy = (success_count / len(test_symbols)) * 100
    print(f"\n📊 数据准确性: {accuracy:.1f}% ({success_count}/{len(test_symbols)})")
    
    if accuracy >= 80:
        print("✅ 数据质量良好，可以正常使用")
    else:
        print("⚠️ 数据质量需要关注")
    
    input("\n按回车键返回菜单...")

def trend_analysis(system):
    """走势趋势分析"""
    print("🔄 分析市场走势趋势...")
    
    symbols, _ = system.get_dynamic_symbol_ranking()
    
    if not symbols:
        print("❌ 无法获取数据")
        return
    
    # 统计各种趋势
    trend_stats = {
        "强势上涨": 0,
        "温和上涨": 0,
        "震荡整理": 0,
        "温和下跌": 0,
        "强势下跌": 0
    }
    
    total_analyzed = 0
    
    for symbol in symbols[:30]:
        try:
            price_data = system.get_real_time_price(symbol)
            if not price_data:
                continue
            
            change_24h = price_data['change_24h']
            multi_changes = price_data.get('multi_timeframe_changes', {})
            
            # 短期趋势判断
            short_changes = [multi_changes.get(tf, {}).get('change_pct', 0) for tf in ['1m', '3m', '5m']]
            avg_short = sum(short_changes) / len(short_changes) if short_changes else 0
            
            # 趋势分类
            if change_24h > 5 and avg_short > 1:
                trend_stats["强势上涨"] += 1
            elif change_24h > 0 and avg_short > 0:
                trend_stats["温和上涨"] += 1
            elif change_24h < -5 and avg_short < -1:
                trend_stats["强势下跌"] += 1
            elif change_24h < 0 and avg_short < 0:
                trend_stats["温和下跌"] += 1
            else:
                trend_stats["震荡整理"] += 1
            
            total_analyzed += 1
            
        except Exception as e:
            continue
    
    clear_screen()
    print("📈 市场走势趋势分析")
    print("=" * 60)
    print(f"📊 分析币种数量: {total_analyzed}")
    print("-" * 60)
    
    for trend, count in trend_stats.items():
        percentage = (count / total_analyzed * 100) if total_analyzed > 0 else 0
        bar_length = int(percentage / 2)  # 50个字符为100%
        bar = "█" * bar_length + "░" * (50 - bar_length)
        
        print(f"{trend:<8} | {bar} | {count:>3}个 ({percentage:>5.1f}%)")
    
    print("-" * 60)
    
    # 市场总体判断
    up_count = trend_stats["强势上涨"] + trend_stats["温和上涨"]
    down_count = trend_stats["强势下跌"] + trend_stats["温和下跌"]
    
    if up_count > down_count * 1.5:
        market_trend = "🚀 市场整体偏强势"
    elif down_count > up_count * 1.5:
        market_trend = "📉 市场整体偏弱势"
    else:
        market_trend = "➡️ 市场整体震荡"
    
    print(f"\n🎯 市场总体趋势: {market_trend}")
    
    input("\n按回车键返回菜单...")

def auto_trend_monitor(system):
    """自动走势监控 - 循环显示所有功能"""
    cycle_count = 0

    while True:
        try:
            cycle_count += 1

            # 每个周期显示不同内容
            if cycle_count % 4 == 1:
                # 实时走势监控
                auto_real_time_monitor(system)
            elif cycle_count % 4 == 2:
                # 动态排行榜
                auto_ranking_display(system)
            elif cycle_count % 4 == 3:
                # 多时间周期分析
                auto_timeframe_analysis(system)
            else:
                # 趋势统计分析
                auto_trend_statistics(system)

            # 等待15秒后切换到下一个功能
            print(f"\n⏰ 15秒后自动切换功能... (周期 {cycle_count}) | 按 Ctrl+C 退出")
            time.sleep(15)

        except KeyboardInterrupt:
            print("\n👋 自动监控已停止")
            break
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(5)

def auto_real_time_monitor(system):
    """自动实时监控"""
    clear_screen()
    print("🚀 自动走势监控 - 实时数据")
    print("=" * 120)
    print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    symbols, _ = system.get_dynamic_symbol_ranking()

    if not symbols:
        print("❌ 无法获取数据")
        return

    print(f"\n📈 TOP20 活跃币种实时走势")
    print("-" * 120)
    print("排名 | 币种        | 当前价格      | 24h涨跌    | 1M    | 3M    | 5M    | 15M   | 成交量    | 趋势")
    print("-" * 120)

    for i, symbol in enumerate(symbols[:20], 1):
        try:
            price_data = system.get_real_time_price(symbol)
            if not price_data:
                continue

            symbol_short = symbol.replace('-USDT-SWAP', '')
            current_price = price_data['last_price']
            change_24h = price_data['change_24h']
            vol_24h = price_data['vol_24h'] / 10000

            multi_changes = price_data.get('multi_timeframe_changes', {})

            # 格式化显示
            change_24h_display = format_change_display(change_24h)

            def short_format(pct):
                if pct > 1:
                    return f"🟢{pct:+.1f}"
                elif pct < -1:
                    return f"🔴{pct:.1f}"
                else:
                    return f"⚪{pct:+.1f}"

            change_1m = short_format(multi_changes.get('1m', {}).get('change_pct', 0))
            change_3m = short_format(multi_changes.get('3m', {}).get('change_pct', 0))
            change_5m = short_format(multi_changes.get('5m', {}).get('change_pct', 0))
            change_15m = short_format(multi_changes.get('15m', {}).get('change_pct', 0))

            # 趋势分析
            changes = [multi_changes.get(tf, {}).get('change_pct', 0) for tf in ['1m', '3m', '5m', '15m']]
            positive_count = sum(1 for c in changes if c > 0)

            if positive_count >= 3:
                trend = "🚀 强势"
            elif positive_count >= 2:
                trend = "📈 上涨"
            elif positive_count <= 1:
                trend = "📉 下跌"
            else:
                trend = "➡️ 震荡"

            vol_display = f"🔥{vol_24h:.0f}万" if vol_24h > 1000 else f"🟡{vol_24h:.0f}万"

            print(f"{i:>3}  | {symbol_short:<10} | {current_price:>12.6f} | {change_24h_display:<10} | {change_1m:<5} | {change_3m:<5} | {change_5m:<5} | {change_15m:<5} | {vol_display:<9} | {trend}")

        except Exception as e:
            continue

def auto_ranking_display(system):
    """自动排行榜显示"""
    clear_screen()
    print("🏆 自动走势监控 - 动态排行榜")
    print("=" * 100)
    print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    symbols, ranking_data = system.get_dynamic_symbol_ranking()

    if not ranking_data:
        print("❌ 无法获取排行榜数据")
        return

    print(f"\n🏆 TOP25 综合排行榜 (智能评分)")
    print("-" * 100)
    print("排名 | 币种名称      | 当前价格      | 24h涨跌    | 成交量(万)  | 综合评分 | 推荐度")
    print("-" * 100)

    for i, ticker in enumerate(ranking_data[:25], 1):
        symbol = ticker['instId'].replace('-USDT-SWAP', '')
        price = float(ticker['last'])
        change_24h = float(ticker.get('chg24h', ticker.get('changePercent24h', ticker.get('priceChangePercent', 0))))
        vol_24h = float(ticker['vol24h']) / 10000
        score = ticker['composite_score']

        change_display = format_change_display(change_24h)

        if score >= 80:
            recommend = "⭐⭐⭐⭐⭐"
        elif score >= 60:
            recommend = "⭐⭐⭐⭐"
        elif score >= 40:
            recommend = "⭐⭐⭐"
        else:
            recommend = "⭐⭐"

        print(f"{i:>3}  | {symbol:<12} | {price:>12.6f} | {change_display:<10} | {vol_24h:>9.0f} | {score:>7.1f} | {recommend}")

def auto_timeframe_analysis(system):
    """自动多时间周期分析"""
    clear_screen()
    print("⏱️ 自动走势监控 - 多时间周期分析")
    print("=" * 110)
    print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    symbols, _ = system.get_dynamic_symbol_ranking()

    if not symbols:
        print("❌ 无法获取数据")
        return

    print(f"\n📊 TOP20 多时间周期涨跌幅")
    print("-" * 110)
    print("币种名称      | 当前价格      | 24h涨跌    | 1分钟     | 3分钟     | 5分钟     | 15分钟    | 趋势判断")
    print("-" * 110)

    for symbol in symbols[:20]:
        try:
            price_data = system.get_real_time_price(symbol)
            if not price_data:
                continue

            symbol_short = symbol.replace('-USDT-SWAP', '')
            current_price = price_data['last_price']
            change_24h = price_data['change_24h']
            multi_changes = price_data.get('multi_timeframe_changes', {})

            change_24h_display = format_change_display(change_24h)
            change_1m_display = format_change_display(multi_changes.get('1m', {}).get('change_pct', 0))
            change_3m_display = format_change_display(multi_changes.get('3m', {}).get('change_pct', 0))
            change_5m_display = format_change_display(multi_changes.get('5m', {}).get('change_pct', 0))
            change_15m_display = format_change_display(multi_changes.get('15m', {}).get('change_pct', 0))

            # 趋势判断
            changes = [multi_changes.get(tf, {}).get('change_pct', 0) for tf in ['1m', '3m', '5m', '15m']]
            positive_count = sum(1 for c in changes if c > 0)

            if positive_count >= 3:
                trend = "🚀 强势上涨"
            elif positive_count >= 2:
                trend = "📈 温和上涨"
            elif positive_count <= 1:
                trend = "📉 下跌趋势"
            else:
                trend = "➡️ 震荡整理"

            print(f"{symbol_short:<12} | {current_price:>12.6f} | {change_24h_display:<10} | {change_1m_display:<9} | {change_3m_display:<9} | {change_5m_display:<9} | {change_15m_display:<9} | {trend}")

        except Exception as e:
            continue

def auto_trend_statistics(system):
    """自动趋势统计"""
    clear_screen()
    print("📈 自动走势监控 - 市场趋势统计")
    print("=" * 80)
    print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    symbols, _ = system.get_dynamic_symbol_ranking()

    if not symbols:
        print("❌ 无法获取数据")
        return

    # 统计趋势
    trend_stats = {"强势上涨": 0, "温和上涨": 0, "震荡整理": 0, "温和下跌": 0, "强势下跌": 0}
    total_analyzed = 0

    for symbol in symbols[:30]:
        try:
            price_data = system.get_real_time_price(symbol)
            if not price_data:
                continue

            change_24h = price_data['change_24h']
            multi_changes = price_data.get('multi_timeframe_changes', {})

            short_changes = [multi_changes.get(tf, {}).get('change_pct', 0) for tf in ['1m', '3m', '5m']]
            avg_short = sum(short_changes) / len(short_changes) if short_changes else 0

            if change_24h > 5 and avg_short > 1:
                trend_stats["强势上涨"] += 1
            elif change_24h > 0 and avg_short > 0:
                trend_stats["温和上涨"] += 1
            elif change_24h < -5 and avg_short < -1:
                trend_stats["强势下跌"] += 1
            elif change_24h < 0 and avg_short < 0:
                trend_stats["温和下跌"] += 1
            else:
                trend_stats["震荡整理"] += 1

            total_analyzed += 1

        except Exception as e:
            continue

    print(f"\n📊 市场趋势分布 (分析 {total_analyzed} 个币种)")
    print("-" * 80)

    for trend, count in trend_stats.items():
        percentage = (count / total_analyzed * 100) if total_analyzed > 0 else 0
        bar_length = int(percentage / 2)
        bar = "█" * bar_length + "░" * (50 - bar_length)

        print(f"{trend:<8} | {bar} | {count:>3}个 ({percentage:>5.1f}%)")

    # 市场总体判断
    up_count = trend_stats["强势上涨"] + trend_stats["温和上涨"]
    down_count = trend_stats["强势下跌"] + trend_stats["温和下跌"]

    if up_count > down_count * 1.5:
        market_trend = "🚀 市场整体偏强势"
    elif down_count > up_count * 1.5:
        market_trend = "📉 市场整体偏弱势"
    else:
        market_trend = "➡️ 市场整体震荡"

    print(f"\n🎯 市场总体趋势: {market_trend}")

def main():
    """主函数 - 自动模式"""
    try:
        print("🚀 启动自动走势监控系统...")
        system = OKXOptimizedTradingSystem()

        print("✅ 系统初始化完成")
        print("📊 开始自动循环监控...")
        print("💡 功能循环: 实时监控 → 排行榜 → 多时间周期 → 趋势统计")
        print("⏰ 每个功能显示15秒，按 Ctrl+C 可退出")
        time.sleep(3)

        auto_trend_monitor(system)

    except KeyboardInterrupt:
        print("\n👋 自动监控已停止")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
