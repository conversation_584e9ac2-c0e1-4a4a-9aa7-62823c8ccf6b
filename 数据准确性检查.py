#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OKX交易系统数据准确性检查工具
全面验证所有币种的数据准确性和信号可靠性
"""

import time
from datetime import datetime
from okx_trading_system_optimized import OKXOptimizedTradingSystem

def print_banner():
    """显示横幅"""
    print("=" * 80)
    print("🔍 OKX交易系统数据准确性检查")
    print("=" * 80)
    print("📊 检查内容: 币种列表、K线数据、实时价格、技术指标、信号准确性")
    print("=" * 80)

def check_symbol_list(system):
    """检查币种列表准确性"""
    print("\n📋 检查币种列表...")
    try:
        symbols = system.get_all_symbols()
        if not symbols:
            print("   ❌ 无法获取币种列表")
            return False, []
        
        print(f"   ✅ 获取到 {len(symbols)} 个币种")
        
        # 验证币种格式
        valid_symbols = []
        invalid_count = 0
        
        for symbol in symbols:
            if symbol.endswith('-USDT-SWAP') and len(symbol.split('-')) == 3:
                valid_symbols.append(symbol)
            else:
                invalid_count += 1
        
        if invalid_count > 0:
            print(f"   ⚠️ 发现 {invalid_count} 个格式异常的币种")
        
        print(f"   📊 有效币种: {len(valid_symbols)} 个")
        print(f"   🔝 前10个币种: {', '.join(valid_symbols[:10])}")
        
        return True, valid_symbols[:20]  # 返回前20个用于测试
        
    except Exception as e:
        print(f"   ❌ 币种列表检查失败: {e}")
        return False, []

def check_kline_data(system, symbols):
    """检查K线数据准确性"""
    print(f"\n📈 检查K线数据 (测试 {len(symbols)} 个币种)...")
    
    success_count = 0
    error_count = 0
    data_quality_issues = 0
    
    for i, symbol in enumerate(symbols):
        try:
            klines = system.get_klines(symbol, '1m', 50)
            
            if not klines:
                error_count += 1
                if error_count <= 3:
                    print(f"   ❌ {symbol}: 无K线数据")
                continue
            
            # 验证数据完整性
            if len(klines) < 30:
                data_quality_issues += 1
                if data_quality_issues <= 3:
                    print(f"   ⚠️ {symbol}: K线数据不足 ({len(klines)}条)")
                continue
            
            # 验证数据格式
            try:
                for j, kline in enumerate(klines[:5]):  # 检查前5条
                    timestamp = int(kline[0])
                    open_price = float(kline[1])
                    high_price = float(kline[2])
                    low_price = float(kline[3])
                    close_price = float(kline[4])
                    volume = float(kline[5])
                    
                    # 价格逻辑验证
                    if not (high_price >= low_price and 
                           low_price <= open_price <= high_price and
                           low_price <= close_price <= high_price and
                           all(p > 0 for p in [open_price, high_price, low_price, close_price])):
                        raise ValueError(f"价格逻辑错误: O={open_price}, H={high_price}, L={low_price}, C={close_price}")
                
                success_count += 1
                if i < 5:  # 显示前5个成功的
                    latest_price = float(klines[0][4])
                    print(f"   ✅ {symbol}: {len(klines)}条K线, 最新价格: {latest_price:.6f}")
                    
            except (ValueError, IndexError, TypeError) as e:
                data_quality_issues += 1
                if data_quality_issues <= 3:
                    print(f"   ⚠️ {symbol}: 数据格式错误 - {e}")
                
        except Exception as e:
            error_count += 1
            if error_count <= 3:
                print(f"   ❌ {symbol}: K线获取异常 - {e}")
    
    print(f"   📊 K线数据检查结果:")
    print(f"      ✅ 成功: {success_count}/{len(symbols)} ({success_count/len(symbols)*100:.1f}%)")
    print(f"      ⚠️ 质量问题: {data_quality_issues}")
    print(f"      ❌ 错误: {error_count}")
    
    return success_count / len(symbols) > 0.8  # 80%成功率为合格

def check_real_time_prices(system, symbols):
    """检查实时价格准确性"""
    print(f"\n💰 检查实时价格 (测试 {len(symbols)} 个币种)...")
    
    success_count = 0
    error_count = 0
    price_issues = 0
    
    for i, symbol in enumerate(symbols):
        try:
            price_data = system.get_real_time_price(symbol)
            
            if not price_data:
                error_count += 1
                if error_count <= 3:
                    print(f"   ❌ {symbol}: 无价格数据")
                continue
            
            # 验证价格数据完整性
            required_fields = ['last_price', 'high_24h', 'low_24h', 'vol_24h', 'change_24h']
            missing_fields = [field for field in required_fields if field not in price_data]
            
            if missing_fields:
                price_issues += 1
                if price_issues <= 3:
                    print(f"   ⚠️ {symbol}: 缺少字段 {missing_fields}")
                continue
            
            # 验证价格逻辑
            last_price = price_data['last_price']
            high_24h = price_data['high_24h']
            low_24h = price_data['low_24h']
            vol_24h = price_data['vol_24h']
            
            if not (high_24h >= low_24h > 0 and 
                   low_24h <= last_price <= high_24h and
                   vol_24h >= 0):
                price_issues += 1
                if price_issues <= 3:
                    print(f"   ⚠️ {symbol}: 价格逻辑错误 - 最新:{last_price}, 高:{high_24h}, 低:{low_24h}")
                continue
            
            success_count += 1
            if i < 5:  # 显示前5个成功的
                change_24h = price_data['change_24h']
                print(f"   ✅ {symbol}: 价格 {last_price:.6f}, 24h变化 {change_24h:+.2f}%, 成交量 {vol_24h:.0f}")
                
        except Exception as e:
            error_count += 1
            if error_count <= 3:
                print(f"   ❌ {symbol}: 价格获取异常 - {e}")
    
    print(f"   📊 实时价格检查结果:")
    print(f"      ✅ 成功: {success_count}/{len(symbols)} ({success_count/len(symbols)*100:.1f}%)")
    print(f"      ⚠️ 价格问题: {price_issues}")
    print(f"      ❌ 错误: {error_count}")
    
    return success_count / len(symbols) > 0.8

def check_technical_indicators(system, symbols):
    """检查技术指标准确性"""
    print(f"\n📈 检查技术指标 (测试 {min(10, len(symbols))} 个币种)...")
    
    test_symbols = symbols[:10]  # 只测试前10个，避免过长
    success_count = 0
    error_count = 0
    indicator_issues = 0
    
    for symbol in test_symbols:
        try:
            analysis = system.analyze_signal(symbol)
            
            if not analysis:
                error_count += 1
                print(f"   ❌ {symbol}: 信号分析失败")
                continue
            
            # 验证必要字段
            required_fields = ['signal', 'confidence', 'price', 'rsi', 'obv']
            missing_fields = [field for field in required_fields if field not in analysis]
            
            if missing_fields:
                indicator_issues += 1
                print(f"   ⚠️ {symbol}: 缺少指标 {missing_fields}")
                continue
            
            # 验证指标范围
            rsi = analysis['rsi']
            confidence = analysis['confidence']
            
            if not (0 <= rsi <= 100):
                indicator_issues += 1
                print(f"   ⚠️ {symbol}: RSI异常 ({rsi})")
                continue
            
            if not (0 <= confidence <= 100):
                indicator_issues += 1
                print(f"   ⚠️ {symbol}: 置信度异常 ({confidence})")
                continue
            
            # 验证OBV数据
            obv_data = analysis.get('obv', {})
            if not (obv_data.get('ma5') and obv_data.get('ma18')):
                indicator_issues += 1
                print(f"   ⚠️ {symbol}: OBV数据不完整")
                continue
            
            success_count += 1
            signal = analysis['signal']
            print(f"   ✅ {symbol}: 信号={signal}, 置信度={confidence}%, RSI={rsi:.1f}")
            
        except Exception as e:
            error_count += 1
            print(f"   ❌ {symbol}: 指标计算异常 - {e}")
    
    print(f"   📊 技术指标检查结果:")
    print(f"      ✅ 成功: {success_count}/{len(test_symbols)} ({success_count/len(test_symbols)*100:.1f}%)")
    print(f"      ⚠️ 指标问题: {indicator_issues}")
    print(f"      ❌ 错误: {error_count}")
    
    return success_count / len(test_symbols) > 0.8

def generate_accuracy_report(results):
    """生成准确性报告"""
    print("\n" + "=" * 80)
    print("📊 数据准确性检查报告")
    print("=" * 80)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    accuracy_score = (passed_checks / total_checks) * 100
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {check_name}: {status}")
    
    print(f"\n🎯 总体准确性: {accuracy_score:.1f}% ({passed_checks}/{total_checks})")
    
    if accuracy_score >= 90:
        print("✅ 数据准确性优秀，系统可以安全运行")
    elif accuracy_score >= 75:
        print("⚠️ 数据准确性良好，建议关注失败项")
    else:
        print("❌ 数据准确性不足，请检查网络和API配置")
    
    return accuracy_score

def main():
    """主函数"""
    print_banner()
    
    try:
        # 初始化系统
        print("🔧 初始化交易系统...")
        system = OKXOptimizedTradingSystem()
        
        results = {}
        
        # 1. 检查币种列表
        symbol_ok, test_symbols = check_symbol_list(system)
        results["币种列表获取"] = symbol_ok
        
        if not symbol_ok or not test_symbols:
            print("❌ 币种列表检查失败，无法继续后续检查")
            return
        
        # 2. 检查K线数据
        results["K线数据准确性"] = check_kline_data(system, test_symbols)
        
        # 3. 检查实时价格
        results["实时价格准确性"] = check_real_time_prices(system, test_symbols)
        
        # 4. 检查技术指标
        results["技术指标准确性"] = check_technical_indicators(system, test_symbols)
        
        # 5. 生成报告
        accuracy_score = generate_accuracy_report(results)
        
        print(f"\n⏰ 检查完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📋 建议: {'立即开始交易' if accuracy_score >= 90 else '检查失败项后再交易' if accuracy_score >= 75 else '修复问题后重新检查'}")
        
    except Exception as e:
        print(f"❌ 检查过程异常: {e}")

if __name__ == "__main__":
    main()
