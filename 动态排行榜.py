#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OKX币种动态排行榜
实时显示币种排名，支持多种排序方式
"""

import time
import os
from datetime import datetime
from okx_trading_system_optimized import OKXOptimizedTradingSystem

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """显示标题"""
    print("🏆" * 50)
    print("           OKX币种动态排行榜 - 实时更新")
    print("🏆" * 50)
    print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def display_detailed_ranking(ranking_data, sort_type="综合"):
    """显示详细排行榜"""
    if not ranking_data:
        print("⚠️ 暂无排行榜数据")
        return
    
    print(f"📊 排序方式: {sort_type}")
    print("=" * 120)
    print("排名 | 币种名称        | 当前价格        | 24h涨跌      | 成交量(万)    | 波动率   | 综合评分 | 推荐度     | 市场热度")
    print("-" * 120)
    
    for i, ticker in enumerate(ranking_data[:30], 1):
        symbol = ticker['instId'].replace('-USDT-SWAP', '')
        price = float(ticker['last'])
        change_24h = float(ticker.get('chg24h', ticker.get('changePercent24h', ticker.get('priceChangePercent', 0))))
        vol_24h = float(ticker['vol24h']) / 10000  # 转换为万
        score = ticker['composite_score']
        
        # 计算波动率 (简化版)
        volatility = abs(change_24h)
        
        # 涨跌幅颜色标识
        if change_24h > 10:
            change_display = f"🚀🚀 +{change_24h:.2f}%"
        elif change_24h > 5:
            change_display = f"🚀 +{change_24h:.2f}%"
        elif change_24h > 2:
            change_display = f"📈 +{change_24h:.2f}%"
        elif change_24h > 0:
            change_display = f"🟢 +{change_24h:.2f}%"
        elif change_24h > -2:
            change_display = f"🔴 {change_24h:.2f}%"
        elif change_24h > -5:
            change_display = f"📉 {change_24h:.2f}%"
        elif change_24h > -10:
            change_display = f"💥 {change_24h:.2f}%"
        else:
            change_display = f"💥💥 {change_24h:.2f}%"
        
        # 推荐度评级
        if score >= 80:
            recommend = "⭐⭐⭐⭐⭐ 强推"
        elif score >= 60:
            recommend = "⭐⭐⭐⭐ 推荐"
        elif score >= 40:
            recommend = "⭐⭐⭐ 一般"
        elif score >= 20:
            recommend = "⭐⭐ 观望"
        else:
            recommend = "⭐ 谨慎"
        
        # 市场热度
        if vol_24h > 10000:
            heat = "🔥🔥🔥 超热"
        elif vol_24h > 5000:
            heat = "🔥🔥 很热"
        elif vol_24h > 1000:
            heat = "🔥 热门"
        elif vol_24h > 100:
            heat = "🟡 温和"
        else:
            heat = "🔵 冷门"
        
        # 波动率显示
        if volatility > 10:
            vol_display = f"🌪️ {volatility:.1f}%"
        elif volatility > 5:
            vol_display = f"⚡ {volatility:.1f}%"
        elif volatility > 2:
            vol_display = f"📊 {volatility:.1f}%"
        else:
            vol_display = f"😴 {volatility:.1f}%"
        
        # 排名标识
        if i <= 3:
            rank_display = f"🏆{i}"
        elif i <= 10:
            rank_display = f"🥇{i}"
        else:
            rank_display = f"📍{i}"
        
        print(f"{rank_display:<4} | {symbol:<14} | {price:>14.6f} | {change_display:<12} | {vol_24h:>12.0f} | {vol_display:<8} | {score:>7.1f} | {recommend:<10} | {heat}")
    
    print("-" * 120)

def display_top_movers(ranking_data):
    """显示涨跌幅排行"""
    if not ranking_data:
        return
    
    # 按涨跌幅排序
    gainers = sorted(ranking_data, key=lambda x: float(x.get('chg24h', x.get('changePercent24h', x.get('priceChangePercent', 0)))), reverse=True)[:10]
    losers = sorted(ranking_data, key=lambda x: float(x.get('chg24h', x.get('changePercent24h', x.get('priceChangePercent', 0)))))[:10]
    
    print("\n📈 今日涨幅榜 TOP10")
    print("-" * 60)
    for i, ticker in enumerate(gainers, 1):
        symbol = ticker['instId'].replace('-USDT-SWAP', '')
        change = float(ticker.get('chg24h', ticker.get('changePercent24h', ticker.get('priceChangePercent', 0))))
        price = float(ticker['last'])
        print(f"{i:>2}. {symbol:<12} 🚀 +{change:>6.2f}%   价格: {price:>10.6f}")

    print("\n📉 今日跌幅榜 TOP10")
    print("-" * 60)
    for i, ticker in enumerate(losers, 1):
        symbol = ticker['instId'].replace('-USDT-SWAP', '')
        change = float(ticker.get('chg24h', ticker.get('changePercent24h', ticker.get('priceChangePercent', 0))))
        price = float(ticker['last'])
        print(f"{i:>2}. {symbol:<12} 💥 {change:>7.2f}%   价格: {price:>10.6f}")

def display_volume_ranking(ranking_data):
    """显示成交量排行"""
    if not ranking_data:
        return
    
    # 按成交量排序
    volume_ranking = sorted(ranking_data, key=lambda x: float(x['vol24h']), reverse=True)[:15]
    
    print("\n💰 成交量排行榜 TOP15")
    print("-" * 80)
    print("排名 | 币种名称        | 24h成交量(万)    | 当前价格        | 24h涨跌")
    print("-" * 80)
    
    for i, ticker in enumerate(volume_ranking, 1):
        symbol = ticker['instId'].replace('-USDT-SWAP', '')
        vol_24h = float(ticker['vol24h']) / 10000
        price = float(ticker['last'])
        change = float(ticker.get('chg24h', ticker.get('changePercent24h', ticker.get('priceChangePercent', 0))))

        change_display = f"+{change:.2f}%" if change >= 0 else f"{change:.2f}%"

        print(f"{i:>3}  | {symbol:<14} | {vol_24h:>15.0f} | {price:>14.6f} | {change_display:>8}")

def main():
    """主函数"""
    try:
        system = OKXOptimizedTradingSystem()
        
        while True:
            clear_screen()
            print_header()
            
            # 获取排行榜数据
            print("🔄 正在获取最新排行榜数据...")
            symbols, ranking_data = system.get_dynamic_symbol_ranking()
            
            if not ranking_data:
                print("❌ 无法获取排行榜数据，请检查网络连接")
                time.sleep(10)
                continue
            
            # 显示综合排行榜
            display_detailed_ranking(ranking_data, "智能综合排序")
            
            # 显示涨跌幅排行
            display_top_movers(ranking_data)
            
            # 显示成交量排行
            display_volume_ranking(ranking_data)
            
            print("\n" + "=" * 120)
            print("📊 数据说明:")
            print("   • 综合评分 = 成交量(60%) + 波动性(30%) + 价格适中性(10%)")
            print("   • 推荐度基于综合评分: ⭐⭐⭐⭐⭐(80+) ⭐⭐⭐⭐(60+) ⭐⭐⭐(40+) ⭐⭐(20+) ⭐(20-)")
            print("   • 市场热度基于24h成交量: 🔥🔥🔥(1万万+) 🔥🔥(5千万+) 🔥(1千万+) 🟡(100万+) 🔵(<100万)")
            print("=" * 120)
            print(f"⏰ 下次更新: 30秒后 | 按 Ctrl+C 退出")
            
            # 等待30秒后更新
            try:
                time.sleep(30)
            except KeyboardInterrupt:
                print("\n👋 排行榜已退出")
                break
                
    except KeyboardInterrupt:
        print("\n👋 排行榜已退出")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
