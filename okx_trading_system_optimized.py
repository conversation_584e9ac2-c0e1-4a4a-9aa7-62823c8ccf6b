#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import requests
import json
import hmac
import hashlib
import base64
import time
import logging
from datetime import datetime, timezone

class OptimizedConfig:
    """优化配置类"""
    # API配置 - 生产环境应使用环境变量
    API_KEY = "73b42f7e-f7c3-4992-aa9e-3a73c170b7e1"
    SECRET_KEY = "92EEE07BB3D540B3E7076070684CBB2F"
    PASSPHRASE = "Wang789654@"
    BASE_URL = "https://www.okx.com"
    
    # ===== 优化交易参数 =====
    MAX_POSITION_SIZE = 200           # 最大仓位大小
    RISK_PER_TRADE = 0.025           # 单笔风险比例 2.5%
    STOP_LOSS_PCT = 0.02             # 止损比例 2%
    SCAN_INTERVAL = 5                # 扫描间隔（秒）

    # ===== 信号阈值 =====
    MIN_BUY_CONFIDENCE = 85          # 买入信号阈值
    HIGH_QUALITY_THRESHOLD = 96      # 高质量信号阈值

    # ===== 风险控制 =====
    MAX_DAILY_LOSS_PCT = 0.05        # 每日最大亏损限制 5%
    MAX_CONCURRENT_POSITIONS = 3     # 最大同时持仓数量
    MAX_TRADES_PER_HOUR = 6          # 每小时最大交易次数
    MIN_TRADE_INTERVAL = 60          # 同币种最小交易间隔（秒）

    # ===== 平仓参数 =====
    SMALL_PROFIT_DRAWDOWN = 2.0     # 小额盈利回撤保护
    MEDIUM_PROFIT_DRAWDOWN = 4.0    # 中等盈利回撤保护
    LARGE_PROFIT_DRAWDOWN = 8.0     # 大额盈利回撤保护
    DRAWDOWN_PCT = 0.3              # 30%回撤保护

class PerformanceTracker:
    """收益跟踪器"""
    def __init__(self):
        self.trades = []
        self.daily_pnl = 0
        self.total_trades = 0
        self.winning_trades = 0
        
    def add_trade(self, trade_data):
        """添加交易记录"""
        self.trades.append({
            'timestamp': datetime.now(),
            'symbol': trade_data['symbol'],
            'side': trade_data['side'],
            'pnl': trade_data.get('pnl', 0),
            'size': trade_data.get('size', 0)
        })
        
        pnl = trade_data.get('pnl', 0)
        self.daily_pnl += pnl
        self.total_trades += 1
        if pnl > 0:
            self.winning_trades += 1
    
    def get_win_rate(self):
        """获取胜率"""
        if self.total_trades == 0:
            return 0
        return (self.winning_trades / self.total_trades) * 100
    
    def generate_report(self):
        """生成收益报告"""
        if not self.trades:
            return "📊 暂无交易数据"
        
        win_rate = self.get_win_rate()
        recent_trades = self.trades[-5:] if len(self.trades) >= 5 else self.trades
        
        report = f"""
📊 收益报告 ({datetime.now().strftime('%H:%M:%S')})
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
💰 今日盈亏: {self.daily_pnl:+.2f} USDT
📈 总交易: {self.total_trades} 笔
🎯 胜率: {win_rate:.1f}%
📋 最近交易: {len(recent_trades)} 笔
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"""
        
        return report

class OKXOptimizedTradingSystem:
    """OKX优化交易系统"""
    
    def __init__(self):
        self.config = OptimizedConfig()
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 风险控制变量
        self.daily_pnl = 0
        self.daily_trades = 0
        self.hourly_trades = {}
        self.last_trade_time = {}
        self.position_peak_profit = {}
        
        # 收益跟踪
        self.performance_tracker = PerformanceTracker()
        
        # 系统启动日志
        self.logger.info("🎯 OKX优化交易系统启动")
        self.logger.info(f"📊 风险参数: 单笔{self.config.RISK_PER_TRADE*100}% | 止损{self.config.STOP_LOSS_PCT*100}% | 日损{self.config.MAX_DAILY_LOSS_PCT*100}%")
        self.logger.info(f"🎯 信号阈值: {self.config.MIN_BUY_CONFIDENCE}% | 最大持仓: {self.config.MAX_CONCURRENT_POSITIONS}个")
        self.logger.info(f"⏰ 扫描间隔: {self.config.SCAN_INTERVAL}秒 | 交易限制: {self.config.MAX_TRADES_PER_HOUR}笔/小时")
    
    def setup_logging(self):
        """设置统一日志系统"""
        # 创建统一日志格式器
        unified_formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-5s | %(name)-8s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 创建统一日志文件处理器
        unified_handler = logging.FileHandler('okx_trading_unified.log', encoding='utf-8')
        unified_handler.setLevel(logging.DEBUG)
        unified_handler.setFormatter(unified_formatter)

        # 配置主日志器
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.DEBUG)
        logger.addHandler(unified_handler)

        # 创建交易专用日志器 (使用同一个文件)
        self.trade_logger = logging.getLogger('TRADE')
        self.trade_logger.setLevel(logging.INFO)
        self.trade_logger.addHandler(unified_handler)

        # 创建信号专用日志器 (使用同一个文件)
        self.signal_logger = logging.getLogger('SIGNAL')
        self.signal_logger.setLevel(logging.INFO)
        self.signal_logger.addHandler(unified_handler)

        # 创建系统专用日志器 (使用同一个文件)
        self.system_logger = logging.getLogger('SYSTEM')
        self.system_logger.setLevel(logging.INFO)
        self.system_logger.addHandler(unified_handler)

        # 防止重复日志
        logger.propagate = False
        self.trade_logger.propagate = False
        self.signal_logger.propagate = False
        self.system_logger.propagate = False
    
    def generate_signature(self, timestamp, method, request_path, body=""):
        """生成签名"""
        message = timestamp + method + request_path + body
        signature = base64.b64encode(
            hmac.new(
                self.config.SECRET_KEY.encode('utf-8'),
                message.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        return signature
    
    def get_headers(self, method, request_path, body=""):
        """获取请求头"""
        timestamp = datetime.now(timezone.utc).isoformat()[:-3] + 'Z'
        signature = self.generate_signature(timestamp, method, request_path, body)
        
        return {
            'OK-ACCESS-KEY': self.config.API_KEY,
            'OK-ACCESS-SIGN': signature,
            'OK-ACCESS-TIMESTAMP': timestamp,
            'OK-ACCESS-PASSPHRASE': self.config.PASSPHRASE,
            'Content-Type': 'application/json'
        }
    
    def get_account_balance(self):
        """获取账户余额"""
        for retry in range(3):  # 重试3次
            try:
                url = f"{self.config.BASE_URL}/api/v5/account/balance"
                headers = self.get_headers('GET', '/api/v5/account/balance')

                response = requests.get(url, headers=headers, timeout=15)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == '0':
                        balances = result.get('data', [])
                        if balances:
                            for balance in balances:
                                for detail in balance.get('details', []):
                                    if detail.get('ccy') == 'USDT':
                                        return float(detail.get('availBal', 0))
                return 0
            except requests.exceptions.Timeout:
                if retry < 2:
                    print(f"⚠️ 获取余额超时，重试 {retry+1}/3", flush=True)
                    time.sleep(2)
                    continue
                else:
                    print("❌ 获取余额最终失败，使用默认值", flush=True)
                    return 100  # 返回默认值避免系统停止
            except Exception as e:
                if retry < 2:
                    print(f"⚠️ 获取余额异常，重试 {retry+1}/3: {e}", flush=True)
                    time.sleep(2)
                    continue
                else:
                    self.logger.error(f"获取余额失败: {e}")
                    return 100  # 返回默认值
    
    def get_positions(self):
        """获取持仓信息"""
        try:
            url = f"{self.config.BASE_URL}/api/v5/account/positions"
            headers = self.get_headers('GET', '/api/v5/account/positions')
            
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == '0':
                    return result.get('data', [])
            return []
        except Exception as e:
            self.logger.error(f"获取持仓失败: {e}")
            return []
    
    def get_klines(self, symbol, timeframe='1m', limit=100):
        """获取K线数据 - 优化版本，确保数据准确性和完整性"""
        for retry in range(3):  # 增加重试次数
            try:
                url = f"{self.config.BASE_URL}/api/v5/market/candles"
                params = {
                    'instId': symbol,
                    'bar': timeframe,
                    'limit': str(limit)
                }

                response = requests.get(url, params=params, timeout=15)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == '0':
                        data = result.get('data', [])
                        if data and len(data) > 0:
                            # 严格验证数据完整性和准确性
                            valid_data = []
                            for candle in data:
                                # 检查K线数据格式：[时间戳, 开盘价, 最高价, 最低价, 收盘价, 成交量]
                                if (len(candle) >= 6 and
                                    all(candle[i] is not None and candle[i] != '' for i in range(6))):

                                    # 验证价格数据的合理性
                                    try:
                                        timestamp = int(candle[0])
                                        open_price = float(candle[1])
                                        high_price = float(candle[2])
                                        low_price = float(candle[3])
                                        close_price = float(candle[4])
                                        volume = float(candle[5])

                                        # 价格逻辑验证：最高价>=最低价，开盘收盘价在高低价范围内
                                        if (high_price >= low_price and
                                            low_price <= open_price <= high_price and
                                            low_price <= close_price <= high_price and
                                            volume >= 0 and
                                            all(price > 0 for price in [open_price, high_price, low_price, close_price])):
                                            valid_data.append(candle)
                                    except (ValueError, TypeError):
                                        continue  # 跳过无效数据

                            if len(valid_data) >= min(limit * 0.8, 50):  # 至少要有80%的有效数据
                                self.logger.debug(f"📊 {symbol} K线数据: {len(valid_data)}/{len(data)} 条有效")
                                return valid_data
                            else:
                                self.logger.warning(f"⚠️ {symbol} 有效K线数据不足: {len(valid_data)}/{len(data)}")

                if retry < 2:
                    time.sleep(1 + retry)  # 递增等待时间
                    continue

            except requests.exceptions.Timeout:
                if retry < 2:
                    self.logger.warning(f"⚠️ {symbol} K线获取超时，重试 {retry+1}/3")
                    time.sleep(2 + retry)
                    continue
            except Exception as e:
                if retry < 2:
                    self.logger.warning(f"⚠️ {symbol} K线获取异常，重试 {retry+1}/3: {str(e)[:50]}")
                    time.sleep(1 + retry)
                    continue
                else:
                    self.logger.error(f"获取K线失败 {symbol}: {e}")

        return []

    def get_multi_timeframe_changes(self, symbol):
        """获取多时间周期涨跌幅数据"""
        timeframes = {
            '1m': 1,
            '3m': 3,
            '5m': 5,
            '15m': 15
        }

        changes = {}

        for tf, minutes in timeframes.items():
            try:
                # 获取对应时间周期的K线数据
                klines = self.get_klines(symbol, tf, 2)  # 只需要2根K线

                if len(klines) >= 2:
                    current_close = float(klines[0][4])  # 最新收盘价
                    previous_close = float(klines[1][4])  # 前一周期收盘价

                    # 计算涨跌幅
                    change_pct = ((current_close - previous_close) / previous_close) * 100
                    changes[tf] = {
                        'change_pct': change_pct,
                        'current_price': current_close,
                        'previous_price': previous_close
                    }
                else:
                    changes[tf] = {
                        'change_pct': 0,
                        'current_price': 0,
                        'previous_price': 0
                    }

            except Exception as e:
                self.logger.warning(f"⚠️ {symbol} {tf}涨跌幅计算失败: {e}")
                changes[tf] = {
                    'change_pct': 0,
                    'current_price': 0,
                    'previous_price': 0
                }

        return changes

    def get_real_time_price(self, symbol):
        """获取实时价格数据 - 优化版本，确保数据准确性"""
        for retry in range(2):
            try:
                url = f"{self.config.BASE_URL}/api/v5/market/ticker"
                params = {'instId': symbol}

                response = requests.get(url, params=params, timeout=10)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == '0':
                        data = result.get('data', [])
                        if data:
                            ticker = data[0]

                            # 验证价格数据的有效性
                            try:
                                last_price = float(ticker.get('last', 0))
                                open_24h = float(ticker.get('open24h', 0))
                                high_24h = float(ticker.get('high24h', 0))
                                low_24h = float(ticker.get('low24h', 0))
                                vol_24h = float(ticker.get('vol24h', 0))
                                change_24h = float(ticker.get('chg24h', ticker.get('changePercent24h', ticker.get('priceChangePercent', 0))))

                                # 数据合理性检查
                                if (last_price > 0 and open_24h > 0 and
                                    high_24h >= low_24h > 0 and vol_24h >= 0 and
                                    low_24h <= last_price <= high_24h):

                                    # 获取多时间周期涨跌幅
                                    multi_changes = self.get_multi_timeframe_changes(symbol)

                                    return {
                                        'symbol': symbol,
                                        'last_price': last_price,
                                        'open_24h': open_24h,
                                        'high_24h': high_24h,
                                        'low_24h': low_24h,
                                        'vol_24h': vol_24h,
                                        'change_24h': change_24h,
                                        'timestamp': ticker.get('ts', ''),
                                        'bid': float(ticker.get('bidPx', 0)),
                                        'ask': float(ticker.get('askPx', 0)),
                                        'multi_timeframe_changes': multi_changes
                                    }
                                else:
                                    self.logger.warning(f"⚠️ {symbol} 价格数据异常: last={last_price}, high={high_24h}, low={low_24h}")

                            except (ValueError, TypeError) as e:
                                self.logger.warning(f"⚠️ {symbol} 价格数据格式错误: {e}")

                if retry < 1:
                    time.sleep(0.5)
                    continue

            except requests.exceptions.Timeout:
                if retry < 1:
                    time.sleep(1)
                    continue
            except Exception as e:
                if retry < 1:
                    time.sleep(0.5)
                    continue
                else:
                    self.logger.error(f"获取实时价格失败 {symbol}: {e}")

        return None
    
    def calculate_ma50(self, klines):
        """计算50周期移动平均线"""
        if len(klines) < 50:
            return None
        
        closes = [float(k[4]) for k in klines[:50]]
        return sum(closes) / len(closes)
    
    def detect_patterns(self, klines):
        """检测K线形态"""
        if len(klines) < 3:
            return {}
        
        patterns = {
            'hammer': False,
            'shooting_star': False,
            'doji': False,
            'bullish_engulfing': False,
            'bearish_engulfing': False
        }
        
        try:
            # 当前K线
            current = klines[0]
            prev = klines[1]
            
            o, h, l, c = float(current[1]), float(current[2]), float(current[3]), float(current[4])
            prev_o, prev_c = float(prev[1]), float(prev[4])
            
            body = abs(c - o)
            total_range = h - l
            upper_shadow = h - max(o, c)
            lower_shadow = min(o, c) - l
            
            # 锤子线
            if (lower_shadow > body * 2 and upper_shadow < body * 0.5 and 
                total_range > 0 and body / total_range < 0.3):
                patterns['hammer'] = True
            
            # 流星线
            if (upper_shadow > body * 2 and lower_shadow < body * 0.5 and 
                total_range > 0 and body / total_range < 0.3):
                patterns['shooting_star'] = True
            
            # 十字星
            if total_range > 0 and body / total_range < 0.1:
                patterns['doji'] = True
            
            # 看涨吞没
            if (c > o and prev_c < prev_o and c > prev_o and o < prev_c):
                patterns['bullish_engulfing'] = True
            
            # 看跌吞没
            if (c < o and prev_c > prev_o and c < prev_o and o > prev_c):
                patterns['bearish_engulfing'] = True
                
        except Exception as e:
            self.logger.error(f"形态检测异常: {e}")
        
        return patterns

    def calculate_technical_indicators(self, klines):
        """计算技术指标"""
        closes = [float(k[4]) for k in klines]
        highs = [float(k[2]) for k in klines]
        lows = [float(k[3]) for k in klines]
        volumes = [float(k[5]) for k in klines]

        # EMA计算
        def calculate_ema(data, period):
            if len(data) < period:
                return None
            multiplier = 2 / (period + 1)
            ema = [sum(data[:period]) / period]  # 第一个EMA用SMA
            for i in range(period, len(data)):
                ema.append((data[i] * multiplier) + (ema[-1] * (1 - multiplier)))
            return ema[-1]

        # RSI计算
        def calculate_rsi(data, period=14):
            if len(data) < period + 1:
                return 50
            deltas = [data[i] - data[i+1] for i in range(len(data)-1)]
            gains = [d if d > 0 else 0 for d in deltas[:period]]
            losses = [-d if d < 0 else 0 for d in deltas[:period]]
            avg_gain = sum(gains) / period if gains else 0
            avg_loss = sum(losses) / period if losses else 0
            if avg_loss == 0:
                return 100
            rs = avg_gain / avg_loss
            return 100 - (100 / (1 + rs))

        # OBV计算 (On Balance Volume)
        def calculate_obv(closes, volumes):
            if len(closes) < 2 or len(volumes) < 2:
                return []

            obv = [0]  # 第一个OBV值设为0
            for i in range(1, len(closes)):
                if closes[i] > closes[i-1]:
                    # 价格上涨，累加成交量
                    obv.append(obv[-1] + volumes[i])
                elif closes[i] < closes[i-1]:
                    # 价格下跌，减去成交量
                    obv.append(obv[-1] - volumes[i])
                else:
                    # 价格不变，OBV不变
                    obv.append(obv[-1])
            return obv

        # OBV均线计算
        def calculate_obv_ma(obv_values, period):
            if len(obv_values) < period:
                return None
            return sum(obv_values[-period:]) / period

        # KDJ计算
        def calculate_kdj(highs, lows, closes, n=9):
            if len(highs) < n:
                return None, None, None

            # 计算RSV
            rsv_list = []
            for i in range(n-1, len(closes)):
                high_n = max(highs[i-n+1:i+1])
                low_n = min(lows[i-n+1:i+1])
                if high_n == low_n:
                    rsv = 50
                else:
                    rsv = (closes[i] - low_n) / (high_n - low_n) * 100
                rsv_list.append(rsv)

            if not rsv_list:
                return None, None, None

            # 计算K值 (SMA)
            k_values = []
            k = 50  # 初始K值
            for rsv in rsv_list:
                k = (2 * k + rsv) / 3  # 简化的SMA计算
                k_values.append(k)

            # 计算D值 (SMA of K)
            d_values = []
            d = 50  # 初始D值
            for k_val in k_values:
                d = (2 * d + k_val) / 3
                d_values.append(d)

            # 计算J值
            if len(k_values) > 0 and len(d_values) > 0:
                k_current = k_values[-1]
                d_current = d_values[-1]
                j = 3 * k_current - 2 * d_current
                return k_current, d_current, j

            return None, None, None

        kdj_k, kdj_d, kdj_j = calculate_kdj(highs, lows, closes)

        # 计算OBV和OBV均线
        obv_values = calculate_obv(closes, volumes)
        obv_ma5 = calculate_obv_ma(obv_values, 5) if len(obv_values) >= 5 else None
        obv_ma18 = calculate_obv_ma(obv_values, 18) if len(obv_values) >= 18 else None

        return {
            'ema5': calculate_ema(closes, 5),
            'ema25': calculate_ema(closes, 25),
            'ema35': calculate_ema(closes, 35),
            'rsi': calculate_rsi(closes),
            'obv': {'values': obv_values, 'ma5': obv_ma5, 'ma18': obv_ma18},
            'kdj': (kdj_k, kdj_d, kdj_j)
        }

    def analyze_signal(self, symbol):
        """分析交易信号 - 优化版本，确保数据准确性和信号可靠性"""
        try:
            # 获取足够的K线数据进行准确分析
            klines = self.get_klines(symbol, '1m', 120)  # 增加数据量
            if len(klines) < 60:  # 降低最低要求但确保足够分析
                self.logger.warning(f"⚠️ {symbol} K线数据不足: {len(klines)}")
                return None

            # 验证K线数据的时间序列正确性
            try:
                timestamps = [int(k[0]) for k in klines]
                if not all(timestamps[i] >= timestamps[i+1] for i in range(len(timestamps)-1)):
                    self.logger.warning(f"⚠️ {symbol} K线时间序列异常")
                    return None
            except (ValueError, IndexError):
                self.logger.error(f"❌ {symbol} K线数据格式错误")
                return None

            current_price = float(klines[0][4])
            ma50 = self.calculate_ma50(klines)
            patterns = self.detect_patterns(klines)
            indicators = self.calculate_technical_indicators(klines)

            # 验证技术指标计算结果
            if not indicators or not all(key in indicators for key in ['ema5', 'ema25', 'ema35', 'rsi', 'obv', 'kdj']):
                self.logger.warning(f"⚠️ {symbol} 技术指标计算失败")
                return None

            # 获取前一周期的指标用于金叉检测
            prev_indicators = None
            if len(klines) > 1:
                prev_indicators = self.calculate_technical_indicators(klines[1:])

            if not ma50:
                self.logger.warning(f"⚠️ {symbol} MA50计算失败")
                return None

            # 计算MA50偏离度
            ma50_diff = ((current_price - ma50) / ma50) * 100

            # 初始化信号
            signal = 'HOLD'
            confidence = 30  # 降低基础置信度
            signals = []
            golden_cross_count = 0
            cross_signals = []  # 专门记录金叉信号

            # 🎯 真正的多指标金叉检测 (核心逻辑优化)

            # 1. EMA金叉检测 (5日线上穿25日线)
            if (indicators['ema5'] and indicators['ema25'] and
                prev_indicators and prev_indicators['ema5'] and prev_indicators['ema25']):

                current_ema5_above = indicators['ema5'] > indicators['ema25']
                prev_ema5_above = prev_indicators['ema5'] > prev_indicators['ema25']

                if current_ema5_above and not prev_ema5_above:
                    signals.append("⚡EMA5上穿EMA25金叉")
                    cross_signals.append("EMA金叉")
                    golden_cross_count += 1
                    confidence += 25
                elif indicators['ema5'] > indicators['ema25'] > indicators['ema35']:
                    signals.append("🔥EMA多头排列")
                    confidence += 15  # 趋势确认，但不算金叉

            # 2. RSI突破关键位置检测
            rsi = indicators['rsi']
            if prev_indicators:
                prev_rsi = prev_indicators['rsi']

                # RSI从超卖区(30以下)突破到30以上
                if rsi > 30 and prev_rsi <= 30:
                    signals.append("⚡RSI突破超卖区")
                    cross_signals.append("RSI金叉")
                    golden_cross_count += 1
                    confidence += 20
                # RSI从50以下突破到50以上
                elif rsi > 50 and prev_rsi <= 50:
                    signals.append("📈RSI突破中性线")
                    cross_signals.append("RSI中性突破")
                    golden_cross_count += 1
                    confidence += 15

            # RSI状态评估
            if rsi > 70:
                signals.append("⚠️RSI超买区域")
                confidence -= 10
            elif rsi < 30:
                signals.append("💡RSI超卖机会")
                confidence += 5

            # 3. OBV金叉死叉检测 (5和18周期均线)
            obv_data = indicators['obv']
            obv_ma5 = obv_data['ma5']
            obv_ma18 = obv_data['ma18']

            if (obv_ma5 and obv_ma18 and prev_indicators):
                prev_obv = prev_indicators['obv']
                prev_obv_ma5 = prev_obv['ma5']
                prev_obv_ma18 = prev_obv['ma18']

                if prev_obv_ma5 and prev_obv_ma18:
                    # OBV5上穿OBV18金叉做多
                    current_obv_above = obv_ma5 > obv_ma18
                    prev_obv_above = prev_obv_ma5 > prev_obv_ma18

                    if current_obv_above and not prev_obv_above:
                        signals.append("⚡OBV金叉做多")
                        cross_signals.append("OBV金叉")
                        golden_cross_count += 1
                        confidence += 25

                        # OBV金叉强度判断
                        obv_strength = (obv_ma5 - obv_ma18) / abs(obv_ma18) * 100
                        if obv_strength > 5:
                            signals.append("🚀OBV强势金叉")
                            confidence += 10

                    # OBV5下穿OBV18死叉做空
                    elif not current_obv_above and prev_obv_above:
                        signals.append("🔻OBV死叉做空")
                        cross_signals.append("OBV死叉")
                        confidence -= 30  # 死叉降低信心度

                    elif current_obv_above:
                        signals.append("📈OBV持续向上")
                        confidence += 8

            # 4. KDJ真正金叉检测
            kdj_k, kdj_d, kdj_j = indicators['kdj']
            if (kdj_k and kdj_d and prev_indicators):
                prev_kdj = prev_indicators['kdj']
                if prev_kdj[0] and prev_kdj[1]:
                    prev_k, prev_d = prev_kdj[0], prev_kdj[1]

                    # K线上穿D线
                    current_k_above = kdj_k > kdj_d
                    prev_k_above = prev_k > prev_d

                    if current_k_above and not prev_k_above and kdj_k < 80:
                        signals.append("⚡KDJ金叉突破")
                        cross_signals.append("KDJ金叉")
                        golden_cross_count += 1
                        confidence += 20

                        # 低位金叉更强
                        if kdj_k < 50:
                            signals.append("💎KDJ低位金叉")
                            confidence += 10

            # 5. 均线突破检测 (价格上穿MA50)
            if len(klines) >= 2:
                prev_price = float(klines[1][4])
                prev_ma50_diff = ((prev_price - ma50) / ma50) * 100

                # 价格从MA50下方突破到上方
                if ma50_diff > 0 and prev_ma50_diff <= 0:
                    signals.append("🚀价格突破MA50")
                    cross_signals.append("MA50突破")
                    golden_cross_count += 1
                    confidence += 20
                elif ma50_diff > 2.0:
                    signals.append("📈价格强势上涨")
                    confidence += 10

            # 🎯 优化的多指标金叉启动点判断
            if golden_cross_count >= 4:
                signals.append("🎯完美金叉启动")
                signal = 'BUY'
                confidence += 40  # 最高置信度
            elif golden_cross_count >= 3:
                signals.append("🎯多指标金叉启动")
                signal = 'BUY'
                confidence += 30
            elif golden_cross_count >= 2:
                signals.append("⚡双指标共振")
                signal = 'BUY'
                confidence += 20
            elif golden_cross_count >= 1:
                signals.append("💡单指标金叉")
                if confidence >= 60:  # 需要其他条件支持
                    signal = 'BUY'
                confidence += 10

            # 形态信号增强
            if patterns['hammer'] or patterns['bullish_engulfing']:
                signals.append("🔨看涨形态")
                confidence += 15
                if signal != 'SELL':
                    signal = 'BUY'

            if patterns['shooting_star'] or patterns['bearish_engulfing']:
                signals.append("⭐看跌形态")
                confidence += 15
                if signal != 'BUY':
                    signal = 'SELL'

            # 成交量确认
            volumes = [float(k[5]) for k in klines[:5]]
            avg_volume = sum(volumes) / len(volumes)
            current_volume = volumes[0]

            if current_volume > avg_volume * 1.5:
                signals.append("📊成交量放大")
                confidence += 10
            elif current_volume < avg_volume * 0.3:
                signals.append("📉成交量萎缩")
                confidence -= 5

            # 记录信号分析日志
            if signal != 'HOLD' or golden_cross_count > 0:
                obv_trend = "金叉" if obv_ma5 and obv_ma18 and obv_ma5 > obv_ma18 else "死叉"
                self.signal_logger.info(
                    f"📊 {symbol} | 信号:{signal} | 置信度:{confidence}% | "
                    f"价格:{current_price:.6f} | RSI:{rsi:.1f} | OBV:{obv_trend} | "
                    f"金叉数:{golden_cross_count} | 信号:{','.join(cross_signals)}"
                )

            return {
                'symbol': symbol,
                'signal': signal,
                'confidence': min(confidence, 95),  # 限制最大置信度
                'price': current_price,
                'ma50': ma50,
                'ma50_diff': ma50_diff,
                'patterns': [k for k, v in patterns.items() if v],
                'signals': signals,
                'golden_cross_count': golden_cross_count,
                'cross_signals': cross_signals,  # 新增：具体的金叉信号列表
                'rsi': rsi,
                'kdj': {'k': kdj_k, 'd': kdj_d, 'j': kdj_j},
                'obv': {'ma5': obv_ma5, 'ma18': obv_ma18}
            }

        except Exception as e:
            self.logger.error(f"信号分析失败 {symbol}: {e}")
            return None

    def check_optimized_risk_limits(self, symbol, confidence):
        """检查优化风险限制"""
        try:
            current_time = datetime.now()
            current_hour = current_time.strftime('%Y-%m-%d-%H')

            # 1. 每日亏损限制
            account_balance = self.get_account_balance()
            daily_loss_limit = account_balance * self.config.MAX_DAILY_LOSS_PCT
            if self.daily_pnl <= -daily_loss_limit:
                return False, f"达到每日亏损限制: {self.daily_pnl:.2f} <= -{daily_loss_limit:.2f} USDT"

            # 2. 最大持仓数量
            positions = self.get_positions()
            active_positions = len([p for p in positions if float(p.get('pos', 0)) != 0])
            if active_positions >= self.config.MAX_CONCURRENT_POSITIONS:
                return False, f"达到最大持仓数量: {active_positions}/{self.config.MAX_CONCURRENT_POSITIONS}"

            # 3. 每小时交易次数
            if current_hour not in self.hourly_trades:
                self.hourly_trades[current_hour] = 0
            if self.hourly_trades[current_hour] >= self.config.MAX_TRADES_PER_HOUR:
                return False, f"达到每小时交易限制: {self.hourly_trades[current_hour]}/{self.config.MAX_TRADES_PER_HOUR}"

            # 4. 交易间隔
            if symbol in self.last_trade_time:
                time_diff = (current_time - self.last_trade_time[symbol]).total_seconds()
                if time_diff < self.config.MIN_TRADE_INTERVAL:
                    return False, f"交易间隔过短: {time_diff:.0f}s < {self.config.MIN_TRADE_INTERVAL}s"

            # 5. 信号质量
            if confidence < self.config.MIN_BUY_CONFIDENCE:
                return False, f"信号质量不足: {confidence}% < {self.config.MIN_BUY_CONFIDENCE}%"

            return True, "通过优化风险检查"

        except Exception as e:
            self.logger.error(f"风险检查异常: {e}")
            return False, f"风险检查异常: {e}"

    def calculate_dynamic_position_size(self, confidence, account_balance):
        """动态计算仓位大小"""
        try:
            # 基础仓位
            base_size = account_balance * self.config.RISK_PER_TRADE

            # 根据信号质量调整
            if confidence >= self.config.HIGH_QUALITY_THRESHOLD:
                # 高质量信号，增加30%仓位
                adjusted_size = base_size * 1.3
            elif confidence >= self.config.MIN_BUY_CONFIDENCE + 5:
                # 中等质量信号，增加15%仓位
                adjusted_size = base_size * 1.15
            else:
                # 普通信号，标准仓位
                adjusted_size = base_size

            # 限制最大仓位
            max_size = min(adjusted_size, self.config.MAX_POSITION_SIZE)

            return max(1, int(max_size))  # 至少1张

        except Exception as e:
            self.logger.error(f"仓位计算异常: {e}")
            return 1

    def place_order(self, symbol, side, size, price):
        """下单"""
        side_chinese = "买入" if side == "buy" else "卖出"

        for retry in range(2):  # 重试2次
            try:
                order_data = {
                    "instId": symbol,
                    "tdMode": "isolated",
                    "side": side,
                    "ordType": "market",
                    "sz": str(int(size)),
                    "posSide": "long" if side == "buy" else "short"
                }

                headers = self.get_headers('POST', '/api/v5/trade/order', json.dumps(order_data))
                url = f"{self.config.BASE_URL}/api/v5/trade/order"

                response = requests.post(url, headers=headers, json=order_data, timeout=15)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == '0':
                        order_id = result.get('data', [{}])[0].get('ordId', 'Unknown')
                        success_msg = f"✅ 下单成功: {symbol} {side_chinese} {size}张"
                        print(success_msg, flush=True)

                        # 详细交易日志
                        self.trade_logger.info(f"🔥 开仓交易 | {symbol} | {side_chinese} | {size}张 | 价格: {price:.6f} | 订单ID: {order_id}")
                        self.logger.info(f"交易执行: {side_chinese} {symbol} {size}张 @{price:.6f}")

                        # 记录交易
                        trade_data = {
                            'symbol': symbol,
                            'side': side,
                            'size': size,
                            'price': price,
                            'pnl': 0,
                            'order_id': order_id
                        }
                        self.performance_tracker.add_trade(trade_data)

                        return result
                    else:
                        error_msg = result.get('msg', '未知错误')
                        # 检查是否是严重错误（不重试）
                        if any(err in error_msg.lower() for err in ['insufficient', 'balance', 'permission', 'forbidden']):
                            print(f"❌ 下单失败: {symbol} {side_chinese} | {error_msg}", flush=True)
                            return result
                        elif retry < 1:
                            print(f"⚠️ 下单失败，重试: {symbol} | {error_msg}", flush=True)
                            time.sleep(2)
                            continue
                        else:
                            print(f"❌ 下单最终失败: {symbol} {side_chinese} | {error_msg}", flush=True)
                            return result
                else:
                    if retry < 1:
                        print(f"⚠️ HTTP错误，重试: {response.status_code}", flush=True)
                        time.sleep(2)
                        continue
                    else:
                        print(f"❌ HTTP最终失败: {response.status_code}", flush=True)
                        return None

            except requests.exceptions.Timeout:
                if retry < 1:
                    print(f"⚠️ 下单超时，重试: {symbol}", flush=True)
                    time.sleep(3)
                    continue
                else:
                    print(f"❌ 下单超时: {symbol}", flush=True)
                    return False
            except Exception as e:
                if retry < 1:
                    print(f"⚠️ 下单异常，重试: {symbol} | {str(e)[:30]}", flush=True)
                    time.sleep(2)
                    continue
                else:
                    print(f"❌ 下单异常: {symbol} | {e}", flush=True)
                    self.logger.error(f"下单异常: {symbol} {side} | {e}")
                    return False

        return False

    def get_dynamic_symbol_ranking(self):
        """获取动态币种排行榜 - 多维度智能排序"""
        for retry in range(3):
            try:
                url = f"{self.config.BASE_URL}/api/v5/market/tickers?instType=SWAP"
                response = requests.get(url, timeout=15)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == '0':
                        tickers = result.get('data', [])

                        # 筛选USDT永续合约，确保数据完整性
                        usdt_tickers = []
                        for t in tickers:
                            if (t['instId'].endswith('-USDT-SWAP') and
                                float(t.get('vol24h', 0)) > 0 and
                                float(t.get('last', 0)) > 0):

                                # 计算综合评分
                                vol_24h = float(t.get('vol24h', 0))
                                # 尝试多个可能的涨跌幅字段名
                                change_24h = abs(float(t.get('chg24h', t.get('changePercent24h', t.get('priceChangePercent', 0)))))
                                last_price = float(t.get('last', 0))

                                # 多维度评分系统
                                volume_score = min(vol_24h / 1000000, 100)  # 成交量评分(百万为基准)
                                volatility_score = min(change_24h * 10, 50)  # 波动性评分
                                price_score = min(last_price / 100, 20) if last_price < 100 else 20  # 价格适中性

                                # 综合评分 = 成交量(60%) + 波动性(30%) + 价格适中性(10%)
                                composite_score = (volume_score * 0.6 +
                                                 volatility_score * 0.3 +
                                                 price_score * 0.1)

                                t['composite_score'] = composite_score
                                usdt_tickers.append(t)

                        if len(usdt_tickers) > 0:
                            # 多重排序策略
                            # 1. 按综合评分排序
                            sorted_by_score = sorted(usdt_tickers,
                                                   key=lambda x: x['composite_score'],
                                                   reverse=True)

                            # 2. 按成交量排序
                            sorted_by_volume = sorted(usdt_tickers,
                                                    key=lambda x: float(x.get('vol24h', 0)),
                                                    reverse=True)

                            # 3. 按涨跌幅排序
                            sorted_by_change = sorted(usdt_tickers,
                                                    key=lambda x: abs(float(x.get('chg24h', x.get('changePercent24h', x.get('priceChangePercent', 0))))),
                                                    reverse=True)

                            # 智能混合排序：前30个综合评分 + 前20个成交量 + 前10个波动性
                            final_ranking = []
                            added_symbols = set()

                            # 添加综合评分前30
                            for ticker in sorted_by_score[:30]:
                                symbol = ticker['instId']
                                if symbol not in added_symbols:
                                    final_ranking.append(ticker)
                                    added_symbols.add(symbol)

                            # 添加成交量前20
                            for ticker in sorted_by_volume[:20]:
                                symbol = ticker['instId']
                                if symbol not in added_symbols and len(final_ranking) < 60:
                                    final_ranking.append(ticker)
                                    added_symbols.add(symbol)

                            # 添加波动性前10
                            for ticker in sorted_by_change[:10]:
                                symbol = ticker['instId']
                                if symbol not in added_symbols and len(final_ranking) < 80:
                                    final_ranking.append(ticker)
                                    added_symbols.add(symbol)

                            # 提取币种符号
                            symbols = [t['instId'] for t in final_ranking]

                            # 记录详细排行榜信息
                            self.logger.info(f"📊 动态排行榜更新: {len(symbols)}个币种")
                            self.logger.info(f"🏆 TOP3: {symbols[:3]}")
                            self.logger.info(f"📈 成交量范围: {float(final_ranking[0]['vol24h']):.0f} - {float(final_ranking[-1]['vol24h']):.0f}")
                            self.logger.info(f"📊 综合评分范围: {final_ranking[0]['composite_score']:.1f} - {final_ranking[-1]['composite_score']:.1f}")

                            print(f"✅ 动态排行榜更新: {len(symbols)} 个币种 (智能排序)", flush=True)
                            print(f"🏆 TOP5: {', '.join(symbols[:5])}", flush=True)

                            return symbols, final_ranking

                print(f"⚠️ 获取排行榜失败，重试 {retry+1}/3", flush=True)
                time.sleep(3)

            except requests.exceptions.Timeout:
                print(f"⚠️ 网络超时，重试 {retry+1}/3", flush=True)
                time.sleep(5)
            except Exception as e:
                print(f"⚠️ 获取排行榜异常，重试 {retry+1}/3: {str(e)[:50]}", flush=True)
                time.sleep(3)

        # 如果所有重试都失败，返回扩展的默认币种列表
        default_symbols = [
            'BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'BNB-USDT-SWAP', 'ADA-USDT-SWAP',
            'SOL-USDT-SWAP', 'XRP-USDT-SWAP', 'DOGE-USDT-SWAP', 'AVAX-USDT-SWAP',
            'MATIC-USDT-SWAP', 'DOT-USDT-SWAP', 'LINK-USDT-SWAP', 'UNI-USDT-SWAP',
            'LTC-USDT-SWAP', 'BCH-USDT-SWAP', 'ATOM-USDT-SWAP', 'FIL-USDT-SWAP',
            'TRX-USDT-SWAP', 'ETC-USDT-SWAP', 'XLM-USDT-SWAP', 'ALGO-USDT-SWAP'
        ]
        self.logger.warning(f"使用默认币种列表: {len(default_symbols)}个")
        print(f"🔄 使用默认币种列表: {len(default_symbols)} 个", flush=True)
        return default_symbols, []

    def get_all_symbols(self):
        """获取所有币种 - 兼容性方法"""
        symbols, _ = self.get_dynamic_symbol_ranking()
        return symbols

    def display_ranking_board(self, ranking_data, top_n=20):
        """显示动态排行榜"""
        if not ranking_data:
            print("⚠️ 暂无排行榜数据", flush=True)
            return

        print("\n" + "=" * 100)
        print("🏆 币种动态排行榜 (智能多维度排序)")
        print("=" * 100)
        print("排名 | 币种名称              | 当前价格      | 24h涨跌    | 成交量(万)   | 综合评分 | 推荐度")
        print("-" * 100)

        for i, ticker in enumerate(ranking_data[:top_n], 1):
            symbol = ticker['instId'].replace('-USDT-SWAP', '')
            price = float(ticker['last'])
            change_24h = float(ticker.get('chg24h', ticker.get('changePercent24h', ticker.get('priceChangePercent', 0))))
            vol_24h = float(ticker['vol24h']) / 10000  # 转换为万
            score = ticker['composite_score']

            # 涨跌幅颜色标识
            if change_24h > 5:
                change_display = f"🚀 +{change_24h:.2f}%"
            elif change_24h > 2:
                change_display = f"📈 +{change_24h:.2f}%"
            elif change_24h > 0:
                change_display = f"🟢 +{change_24h:.2f}%"
            elif change_24h > -2:
                change_display = f"🔴 {change_24h:.2f}%"
            elif change_24h > -5:
                change_display = f"📉 {change_24h:.2f}%"
            else:
                change_display = f"💥 {change_24h:.2f}%"

            # 推荐度评级
            if score >= 80:
                recommend = "⭐⭐⭐⭐⭐"
            elif score >= 60:
                recommend = "⭐⭐⭐⭐"
            elif score >= 40:
                recommend = "⭐⭐⭐"
            elif score >= 20:
                recommend = "⭐⭐"
            else:
                recommend = "⭐"

            # 格式化显示
            print(f"{i:>3}  | {symbol:<20} | {price:>12.6f} | {change_display:<10} | {vol_24h:>10.0f} | {score:>7.1f} | {recommend}")

        print("-" * 100)
        print(f"📊 排行榜更新时间: {datetime.now().strftime('%H:%M:%S')}")
        print(f"🎯 评分算法: 成交量(60%) + 波动性(30%) + 价格适中性(10%)")
        print("=" * 100)

    def check_exit_signals(self):
        """检查平仓信号"""
        try:
            positions = self.get_positions()

            for position in positions:
                if float(position.get('pos', 0)) == 0:
                    continue

                symbol = position['instId']
                pos_size = float(position['pos'])
                unrealized_pnl = float(position.get('upl', 0))

                # 更新最高盈利记录
                if symbol not in self.position_peak_profit:
                    self.position_peak_profit[symbol] = unrealized_pnl
                else:
                    self.position_peak_profit[symbol] = max(self.position_peak_profit[symbol], unrealized_pnl)

                should_close = False
                close_reason = ""

                # 止损检查
                if unrealized_pnl < 0:
                    account_balance = self.get_account_balance()
                    stop_loss_amount = account_balance * self.config.STOP_LOSS_PCT
                    if abs(unrealized_pnl) >= stop_loss_amount:
                        should_close = True
                        close_reason = f"止损: {unrealized_pnl:.2f} <= -{stop_loss_amount:.2f}"

                # 优化回撤保护
                if unrealized_pnl > 0:
                    peak_profit = self.position_peak_profit[symbol]
                    drawdown = peak_profit - unrealized_pnl

                    if peak_profit >= 6.0:  # 大额盈利
                        if drawdown >= self.config.LARGE_PROFIT_DRAWDOWN:
                            should_close = True
                            close_reason = f"大额回撤保护: 回撤{drawdown:.2f}U"
                    elif peak_profit >= 3.0:  # 中等盈利
                        if drawdown >= self.config.MEDIUM_PROFIT_DRAWDOWN:
                            should_close = True
                            close_reason = f"中等回撤保护: 回撤{drawdown:.2f}U"
                    elif peak_profit >= 1.2:  # 小额盈利
                        if drawdown >= self.config.SMALL_PROFIT_DRAWDOWN:
                            should_close = True
                            close_reason = f"小额回撤保护: 回撤{drawdown:.2f}U"

                    # 百分比回撤保护
                    if peak_profit > 0:
                        drawdown_pct = drawdown / peak_profit
                        if drawdown_pct >= self.config.DRAWDOWN_PCT:
                            should_close = True
                            close_reason = f"百分比回撤: {drawdown_pct*100:.1f}%"

                # 执行平仓
                if should_close:
                    side = "sell" if pos_size > 0 else "buy"
                    result = self.place_order(symbol, side, abs(pos_size), 0)

                    if result and result.get('code') == '0':
                        print(f"🔄 平仓执行: {symbol} | 原因: {close_reason}", flush=True)

                        # 详细平仓日志
                        pnl_status = "💰盈利" if unrealized_pnl > 0 else "💸亏损"
                        self.trade_logger.info(f"🔄 平仓交易 | {symbol} | {side} | {abs(pos_size)}张 | {pnl_status}: {unrealized_pnl:+.2f}U | 原因: {close_reason}")
                        self.logger.info(f"平仓完成: {symbol} | {close_reason} | 盈亏: {unrealized_pnl:+.2f}U")

                        # 更新收益记录
                        trade_data = {
                            'symbol': symbol,
                            'side': side,
                            'pnl': unrealized_pnl,
                            'size': abs(pos_size)
                        }
                        self.performance_tracker.add_trade(trade_data)
                        self.daily_pnl += unrealized_pnl

                        # 清除记录
                        if symbol in self.position_peak_profit:
                            del self.position_peak_profit[symbol]

        except Exception as e:
            self.logger.error(f"平仓检查异常: {e}")

    def update_trade_counters(self, symbol):
        """更新交易计数器"""
        current_time = datetime.now()
        current_hour = current_time.strftime('%Y-%m-%d-%H')

        # 更新每小时交易次数
        if current_hour not in self.hourly_trades:
            self.hourly_trades[current_hour] = 0
        self.hourly_trades[current_hour] += 1

        # 更新最后交易时间
        self.last_trade_time[symbol] = current_time

        # 更新每日交易次数
        self.daily_trades += 1

    def test_golden_cross_signals(self, symbol_list=None):
        """测试多指标金叉信号质量"""
        print("🔍 多指标金叉信号测试")
        print("=" * 60)

        if not symbol_list:
            symbol_list = ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'BNB-USDT-SWAP']

        for symbol in symbol_list:
            try:
                analysis = self.analyze_signal(symbol)
                if not analysis:
                    print(f"❌ {symbol}: 无法获取数据")
                    continue

                print(f"\n📊 {symbol} 金叉信号分析:")
                print(f"   当前价格: {analysis['price']:.6f}")
                print(f"   信号类型: {analysis['signal']}")
                print(f"   置信度: {analysis['confidence']}%")
                print(f"   金叉数量: {analysis['golden_cross_count']}")

                if analysis.get('cross_signals'):
                    print(f"   ⚡ 金叉类型: {', '.join(analysis['cross_signals'])}")

                # 技术指标详情
                print(f"   📈 RSI: {analysis['rsi']:.1f}")
                if analysis.get('kdj'):
                    kdj = analysis['kdj']
                    print(f"   📈 KDJ: K={kdj['k']:.1f}, D={kdj['d']:.1f}, J={kdj['j']:.1f}")
                if analysis.get('obv'):
                    obv = analysis['obv']
                    if obv['ma5'] and obv['ma18']:
                        trend = "📈" if obv['ma5'] > obv['ma18'] else "📉"
                        print(f"   {trend} OBV: MA5={obv['ma5']:.0f}, MA18={obv['ma18']:.0f}")

                # 信号评级
                if analysis['golden_cross_count'] >= 4:
                    print("   🎯 评级: 完美金叉启动 ⭐⭐⭐⭐⭐")
                elif analysis['golden_cross_count'] >= 3:
                    print("   🎯 评级: 多指标金叉启动 ⭐⭐⭐⭐")
                elif analysis['golden_cross_count'] >= 2:
                    print("   ⚡ 评级: 双指标共振 ⭐⭐⭐")
                elif analysis['golden_cross_count'] >= 1:
                    print("   💡 评级: 单指标金叉 ⭐⭐")
                else:
                    print("   ➡️ 评级: 无金叉信号 ⭐")

                print(f"   📋 所有信号: {', '.join(analysis['signals'])}")

            except Exception as e:
                print(f"❌ {symbol} 分析失败: {e}")

        print("\n" + "=" * 60)

    def run_strategy(self):
        """运行优化策略"""
        print("🚀 启动OKX优化交易系统", flush=True)
        print("=" * 60, flush=True)
        print(f"💰 风险控制: {self.config.RISK_PER_TRADE*100:.1f}%单笔, {self.config.MAX_DAILY_LOSS_PCT*100:.0f}%日损限制", flush=True)
        print(f"📊 信号阈值: {self.config.MIN_BUY_CONFIDENCE}%", flush=True)
        print(f"🔄 扫描间隔: {self.config.SCAN_INTERVAL}秒", flush=True)
        print("=" * 60, flush=True)

        scan_count = 0

        while True:
            try:
                scan_count += 1
                current_time = datetime.now().strftime('%H:%M:%S')
                current_balance = self.get_account_balance()

                print(f"\n🔍 第{scan_count}次扫描 - {current_time}", flush=True)
                print(f"💰 当前余额: {current_balance:.2f} USDT", flush=True)

                # 详细扫描日志
                self.logger.info(f"🔍 开始第{scan_count}次市场扫描 | 余额: {current_balance:.2f}U | 时间: {current_time}")

                # 检查平仓信号
                positions = self.get_positions()
                active_positions = [p for p in positions if float(p.get('pos', 0)) != 0]
                if active_positions:
                    print(f"📊 当前持仓: {len(active_positions)} 个", flush=True)
                    for pos in active_positions:
                        symbol = pos['instId']
                        size = float(pos['pos'])
                        pnl = float(pos.get('upl', 0))
                        print(f"   {symbol}: {size:+.0f}张, 盈亏: {pnl:+.2f} USDT", flush=True)
                else:
                    print("📊 当前持仓: 无", flush=True)

                self.check_exit_signals()

                # 获取动态排行榜
                symbols, ranking_data = self.get_dynamic_symbol_ranking()
                if not symbols:
                    print("⚠️ 无法获取币种排行榜，等待下次扫描", flush=True)
                    time.sleep(self.config.SCAN_INTERVAL * 2)  # 延长等待时间
                    continue

                # 每5次扫描显示一次完整排行榜
                if scan_count % 5 == 1:
                    self.display_ranking_board(ranking_data, top_n=15)

                print(f"\n📊 持续分析 {len(symbols)} 个币种走势和价格...", flush=True)
                print("币种名称              |    当前价格    |   24h涨跌   | 1M | 3M | 5M | 15M | 量级 | 信号 | 置信度", flush=True)
                print("=" * 120, flush=True)

                # 分析信号
                signals_found = 0
                high_quality_signals = 0

                # 数据验证计数器
                valid_analysis_count = 0
                data_error_count = 0

                for i, symbol in enumerate(symbols):
                    try:
                        # 分析信号，确保数据准确性
                        analysis = self.analyze_signal(symbol)
                        if not analysis:
                            data_error_count += 1
                            if data_error_count <= 5:  # 只显示前5个错误，避免刷屏
                                print(f"❌ {symbol}: 信号分析失败", flush=True)
                            continue

                        # 获取实时价格数据并验证
                        price_data = self.get_real_time_price(symbol)
                        if price_data:
                            current_price = price_data['last_price']
                            change_24h = price_data['change_24h']
                            vol_24h = price_data['vol_24h']
                            multi_changes = price_data.get('multi_timeframe_changes', {})

                            # 数据一致性检查
                            price_diff = abs(current_price - analysis['price']) / analysis['price']
                            if price_diff > 0.05:  # 价格差异超过5%可能有问题
                                self.logger.warning(f"⚠️ {symbol} 价格数据不一致: 实时={current_price:.6f}, 分析={analysis['price']:.6f}")

                            # 24小时涨跌幅分类
                            if change_24h > 2:
                                trend = f"🚀 +{change_24h:.2f}%"
                            elif change_24h > 0.5:
                                trend = f"📈 +{change_24h:.2f}%"
                            elif change_24h < -2:
                                trend = f"💥 {change_24h:.2f}%"
                            elif change_24h < -0.5:
                                trend = f"📉 {change_24h:.2f}%"
                            else:
                                trend = f"➡️ {change_24h:+.2f}%"

                            # 多时间周期涨跌幅显示
                            def format_change(change_pct):
                                if change_pct > 1:
                                    return f"🟢{change_pct:+.1f}"
                                elif change_pct > 0:
                                    return f"🟢{change_pct:+.1f}"
                                elif change_pct < -1:
                                    return f"🔴{change_pct:.1f}"
                                elif change_pct < 0:
                                    return f"🔴{change_pct:.1f}"
                                else:
                                    return f"⚪{change_pct:+.1f}"

                            change_1m = format_change(multi_changes.get('1m', {}).get('change_pct', 0))
                            change_3m = format_change(multi_changes.get('3m', {}).get('change_pct', 0))
                            change_5m = format_change(multi_changes.get('5m', {}).get('change_pct', 0))
                            change_15m = format_change(multi_changes.get('15m', {}).get('change_pct', 0))

                            # 成交量等级分类
                            if vol_24h > 5000000:
                                vol_level = "🔥超高"
                            elif vol_24h > 1000000:
                                vol_level = "🔥高"
                            elif vol_24h > 100000:
                                vol_level = "🟡中"
                            elif vol_24h > 10000:
                                vol_level = "🔵低"
                            else:
                                vol_level = "⚪极低"
                        else:
                            current_price = analysis['price']
                            trend = "❓ 无数据"
                            vol_level = "❓"
                            change_1m = change_3m = change_5m = change_15m = "❓"

                        # 信号准确显示
                        signal_chinese = "🟢买入" if analysis['signal'] == "BUY" else "🔴卖出" if analysis['signal'] == "SELL" else "⚪持有"

                        # 置信度颜色标识
                        confidence = analysis['confidence']
                        if confidence >= 80:
                            conf_display = f"🟢{confidence}%"
                        elif confidence >= 60:
                            conf_display = f"🟡{confidence}%"
                        else:
                            conf_display = f"🔴{confidence}%"

                        # 显示完整准确信息，包含多时间周期
                        if price_data:
                            print(f"{symbol:20} | 价格: {current_price:>10.6f} | {trend:>12} | {change_1m:>4} | {change_3m:>4} | {change_5m:>4} | {change_15m:>5} | {vol_level:>6} | {signal_chinese:>6} | {conf_display:>7}", flush=True)
                        else:
                            print(f"{symbol:20} | 价格: {analysis['price']:>10.6f} | {trend:>12} | {change_1m:>4} | {change_3m:>4} | {change_5m:>4} | {change_15m:>5} | {vol_level:>6} | {signal_chinese:>6} | {conf_display:>7}", flush=True)

                        valid_analysis_count += 1

                        # 统计所有信号 (降低阈值)
                        if analysis['confidence'] >= 60:
                            signals_found += 1
                            if analysis['confidence'] >= self.config.HIGH_QUALITY_THRESHOLD:
                                high_quality_signals += 1

                                # 显示高质量信号的详细信息
                                print(f"🎯 高质量信号: {symbol} | 金叉数量: {analysis['golden_cross_count']}", flush=True)

                                # 显示具体的金叉信号
                                if analysis.get('cross_signals'):
                                    print(f"   ⚡ 金叉信号: {', '.join(analysis['cross_signals'])}", flush=True)

                                # 显示技术指标状态
                                if analysis.get('rsi'):
                                    print(f"   📊 RSI: {analysis['rsi']:.1f}", flush=True)
                                if analysis.get('kdj'):
                                    kdj = analysis['kdj']
                                    print(f"   📊 KDJ: K={kdj['k']:.1f}, D={kdj['d']:.1f}, J={kdj['j']:.1f}", flush=True)
                                if analysis.get('obv'):
                                    obv = analysis['obv']
                                    if obv['ma5'] and obv['ma18']:
                                        trend = "📈" if obv['ma5'] > obv['ma18'] else "📉"
                                        print(f"   📊 {trend} OBV: MA5={obv['ma5']:.0f}, MA18={obv['ma18']:.0f}", flush=True)

                                # 显示K线形态
                                if analysis.get('patterns'):
                                    patterns_chinese = []
                                    for pattern in analysis['patterns']:
                                        if pattern == 'hammer': patterns_chinese.append('锤头线')
                                        elif pattern == 'shooting_star': patterns_chinese.append('流星线')
                                        elif pattern == 'doji': patterns_chinese.append('十字星')
                                        elif pattern == 'bullish_engulfing': patterns_chinese.append('看涨吞没')
                                        elif pattern == 'bearish_engulfing': patterns_chinese.append('看跌吞没')
                                        else: patterns_chinese.append(pattern)
                                    print(f"   🔨 形态: {', '.join(patterns_chinese)}", flush=True)

                                # 显示所有信号
                                if analysis.get('signals'):
                                    print(f"   📋 信号详情: {', '.join(analysis['signals'])}", flush=True)

                        if analysis['signal'] == 'HOLD':
                            continue

                        # 检查风险限制
                        risk_ok, risk_msg = self.check_optimized_risk_limits(symbol, analysis['confidence'])
                        if not risk_ok:
                            print(f"⚠️ 风险限制: {symbol} - {risk_msg}", flush=True)
                            continue

                        # 计算动态仓位
                        account_balance = self.get_account_balance()
                        position_size = self.calculate_dynamic_position_size(analysis['confidence'], account_balance)

                        if position_size >= 1:
                            side = "buy" if analysis['signal'] == 'BUY' else "sell"
                            result = self.place_order(symbol, side, position_size, analysis['price'])

                            if result and result.get('code') == '0':
                                self.update_trade_counters(symbol)
                                print(f"✅ 优化交易: {symbol} {side} {position_size}张 | 置信度: {analysis['confidence']}%", flush=True)

                        # 实时进度显示
                        if (i + 1) % 5 == 0:
                            print(f"⚡ 已分析 {i+1}/{len(symbols)} 个币种...", flush=True)

                    except Exception as e:
                        # 只记录非网络相关的错误
                        if not any(err in str(e).lower() for err in ['timeout', 'connection', 'network', 'read timed out']):
                            self.logger.error(f"分析异常 {symbol}: {e}")
                        continue

                # 扫描总结
                print("=" * 80, flush=True)
                print(f"📊 扫描总结: 发现 {signals_found} 个信号 (其中 {high_quality_signals} 个高质量)", flush=True)
                print(f"💼 今日交易: {self.daily_trades} 笔 | 今日盈亏: {self.daily_pnl:+.2f} USDT", flush=True)
                print(f"⏰ 下次扫描: {self.config.SCAN_INTERVAL} 秒后", flush=True)

                # 详细扫描总结日志
                self.logger.info(
                    f"📊 扫描完成 #{scan_count} | 信号: {signals_found}个({high_quality_signals}高质量) | "
                    f"今日: {self.daily_trades}笔交易 {self.daily_pnl:+.2f}U"
                )

                # 显示收益报告
                if scan_count % 5 == 0:  # 每5次扫描显示一次
                    report = self.performance_tracker.generate_report()
                    print(report, flush=True)

                # 短暂等待后继续分析
                time.sleep(self.config.SCAN_INTERVAL)

            except KeyboardInterrupt:
                print("\n👋 用户停止交易系统", flush=True)
                self.logger.info("用户手动停止交易系统")
                break
            except Exception as e:
                print(f"❌ 系统异常: {e}", flush=True)
                self.logger.error(f"系统运行异常: {e}")
                time.sleep(5)

def main():
    """主函数"""
    print("🎯 OKX优化交易系统")
    print("=" * 60)

    import sys

    system = OKXOptimizedTradingSystem()

    # 检查是否有测试参数
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        print("🧪 进入金叉信号测试模式")
        system.test_golden_cross_signals()
        return

    system.run_strategy()

if __name__ == "__main__":
    main()
