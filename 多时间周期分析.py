#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OKX多时间周期涨跌幅分析工具
实时显示1分钟、3分钟、5分钟、15分钟、24小时涨跌幅
"""

import time
import os
from datetime import datetime
from okx_trading_system_optimized import OKXOptimizedTradingSystem

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """显示标题"""
    print("📊" * 60)
    print("                    OKX多时间周期涨跌幅分析 - 实时监控")
    print("📊" * 60)
    print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def format_change_with_color(change_pct, timeframe=""):
    """格式化涨跌幅显示，带颜色标识"""
    if change_pct > 3:
        return f"🚀 +{change_pct:.2f}%"
    elif change_pct > 1:
        return f"📈 +{change_pct:.2f}%"
    elif change_pct > 0:
        return f"🟢 +{change_pct:.2f}%"
    elif change_pct > -1:
        return f"🔴 {change_pct:.2f}%"
    elif change_pct > -3:
        return f"📉 {change_pct:.2f}%"
    else:
        return f"💥 {change_pct:.2f}%"

def analyze_timeframe_trend(changes):
    """分析多时间周期趋势"""
    timeframes = ['1m', '3m', '5m', '15m']
    trend_score = 0
    
    for tf in timeframes:
        change = changes.get(tf, {}).get('change_pct', 0)
        if change > 0:
            trend_score += 1
        elif change < 0:
            trend_score -= 1
    
    if trend_score >= 3:
        return "🚀 强势上涨"
    elif trend_score >= 1:
        return "📈 温和上涨"
    elif trend_score <= -3:
        return "💥 强势下跌"
    elif trend_score <= -1:
        return "📉 温和下跌"
    else:
        return "➡️ 震荡整理"

def display_multi_timeframe_analysis(symbols, system, top_n=30):
    """显示多时间周期分析"""
    print("🔍 正在获取多时间周期数据...")
    
    analysis_data = []
    
    for i, symbol in enumerate(symbols[:top_n]):
        try:
            # 获取实时价格和多时间周期数据
            price_data = system.get_real_time_price(symbol)
            if not price_data:
                continue
                
            current_price = price_data['last_price']
            change_24h = price_data['change_24h']
            vol_24h = price_data['vol_24h']
            multi_changes = price_data.get('multi_timeframe_changes', {})
            
            # 分析趋势
            trend_analysis = analyze_timeframe_trend(multi_changes)
            
            analysis_data.append({
                'symbol': symbol,
                'price': current_price,
                'change_24h': change_24h,
                'vol_24h': vol_24h,
                'multi_changes': multi_changes,
                'trend': trend_analysis
            })
            
        except Exception as e:
            print(f"⚠️ {symbol} 分析失败: {e}")
            continue
    
    if not analysis_data:
        print("❌ 无法获取分析数据")
        return
    
    # 显示详细分析表格
    print("\n📊 多时间周期涨跌幅分析")
    print("=" * 140)
    print("币种名称              | 当前价格        | 24h涨跌      | 1分钟涨跌    | 3分钟涨跌    | 5分钟涨跌    | 15分钟涨跌   | 趋势分析      | 成交量等级")
    print("-" * 140)
    
    for data in analysis_data:
        symbol_short = data['symbol'].replace('-USDT-SWAP', '')
        price = data['price']
        change_24h = data['change_24h']
        vol_24h = data['vol_24h']
        multi_changes = data['multi_changes']
        trend = data['trend']
        
        # 格式化24小时涨跌幅
        change_24h_display = format_change_with_color(change_24h)
        
        # 格式化各时间周期涨跌幅
        change_1m = format_change_with_color(multi_changes.get('1m', {}).get('change_pct', 0))
        change_3m = format_change_with_color(multi_changes.get('3m', {}).get('change_pct', 0))
        change_5m = format_change_with_color(multi_changes.get('5m', {}).get('change_pct', 0))
        change_15m = format_change_with_color(multi_changes.get('15m', {}).get('change_pct', 0))
        
        # 成交量等级
        if vol_24h > 10000000:
            vol_level = "🔥🔥🔥 超高"
        elif vol_24h > 5000000:
            vol_level = "🔥🔥 很高"
        elif vol_24h > 1000000:
            vol_level = "🔥 高"
        elif vol_24h > 100000:
            vol_level = "🟡 中"
        else:
            vol_level = "🔵 低"
        
        print(f"{symbol_short:<20} | {price:>14.6f} | {change_24h_display:<12} | {change_1m:<12} | {change_3m:<12} | {change_5m:<12} | {change_15m:<12} | {trend:<12} | {vol_level}")
    
    print("-" * 140)

def display_timeframe_rankings(analysis_data):
    """显示各时间周期涨跌幅排行"""
    if not analysis_data:
        return
    
    timeframes = {
        '1m': '1分钟',
        '3m': '3分钟', 
        '5m': '5分钟',
        '15m': '15分钟'
    }
    
    print("\n🏆 各时间周期涨跌幅排行榜")
    print("=" * 100)
    
    for tf, tf_name in timeframes.items():
        # 按该时间周期涨跌幅排序
        sorted_data = sorted(analysis_data, 
                           key=lambda x: x['multi_changes'].get(tf, {}).get('change_pct', 0), 
                           reverse=True)
        
        print(f"\n📈 {tf_name}涨幅榜 TOP10:")
        print("-" * 80)
        
        for i, data in enumerate(sorted_data[:10], 1):
            symbol_short = data['symbol'].replace('-USDT-SWAP', '')
            change = data['multi_changes'].get(tf, {}).get('change_pct', 0)
            price = data['price']
            
            if change > 0:
                print(f"{i:>2}. {symbol_short:<15} 🚀 +{change:>6.2f}%   价格: {price:>12.6f}")
        
        print(f"\n📉 {tf_name}跌幅榜 TOP5:")
        print("-" * 80)
        
        for i, data in enumerate(sorted_data[-5:], 1):
            symbol_short = data['symbol'].replace('-USDT-SWAP', '')
            change = data['multi_changes'].get(tf, {}).get('change_pct', 0)
            price = data['price']
            
            if change < 0:
                print(f"{i:>2}. {symbol_short:<15} 💥 {change:>7.2f}%   价格: {price:>12.6f}")

def display_trend_summary(analysis_data):
    """显示趋势汇总"""
    if not analysis_data:
        return
    
    print("\n📊 市场趋势汇总")
    print("=" * 80)
    
    trend_counts = {}
    total_symbols = len(analysis_data)
    
    for data in analysis_data:
        trend = data['trend']
        trend_counts[trend] = trend_counts.get(trend, 0) + 1
    
    for trend, count in sorted(trend_counts.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_symbols) * 100
        print(f"{trend:<15} | {count:>3}个币种 ({percentage:>5.1f}%)")
    
    print("-" * 80)
    
    # 计算平均涨跌幅
    timeframes = ['1m', '3m', '5m', '15m']
    print("\n📈 各时间周期平均涨跌幅:")
    print("-" * 50)
    
    for tf in timeframes:
        changes = [data['multi_changes'].get(tf, {}).get('change_pct', 0) for data in analysis_data]
        avg_change = sum(changes) / len(changes) if changes else 0
        
        tf_display = {'1m': '1分钟', '3m': '3分钟', '5m': '5分钟', '15m': '15分钟'}[tf]
        change_display = format_change_with_color(avg_change)
        
        print(f"{tf_display:<8} | {change_display}")

def main():
    """主函数"""
    try:
        system = OKXOptimizedTradingSystem()
        
        while True:
            clear_screen()
            print_header()
            
            # 获取币种列表
            print("🔄 正在获取币种列表...")
            symbols, ranking_data = system.get_dynamic_symbol_ranking()
            
            if not symbols:
                print("❌ 无法获取币种列表，请检查网络连接")
                time.sleep(10)
                continue
            
            # 多时间周期分析
            analysis_data = []
            
            print(f"📊 分析前{min(30, len(symbols))}个活跃币种的多时间周期数据...")
            
            for symbol in symbols[:30]:
                try:
                    price_data = system.get_real_time_price(symbol)
                    if price_data:
                        multi_changes = price_data.get('multi_timeframe_changes', {})
                        trend_analysis = analyze_timeframe_trend(multi_changes)
                        
                        analysis_data.append({
                            'symbol': symbol,
                            'price': price_data['last_price'],
                            'change_24h': price_data['change_24h'],
                            'vol_24h': price_data['vol_24h'],
                            'multi_changes': multi_changes,
                            'trend': trend_analysis
                        })
                except Exception as e:
                    continue
            
            if analysis_data:
                # 显示多时间周期分析
                display_multi_timeframe_analysis(symbols, system, 25)
                
                # 显示各时间周期排行
                display_timeframe_rankings(analysis_data)
                
                # 显示趋势汇总
                display_trend_summary(analysis_data)
            
            print("\n" + "=" * 140)
            print("📊 数据说明:")
            print("   • 多时间周期分析: 1分钟、3分钟、5分钟、15分钟、24小时涨跌幅对比")
            print("   • 趋势分析: 基于多时间周期一致性判断强势/弱势/震荡")
            print("   • 颜色标识: 🚀强涨 📈温涨 🟢微涨 🔴微跌 📉温跌 💥强跌")
            print("=" * 140)
            print(f"⏰ 下次更新: 30秒后 | 按 Ctrl+C 退出")
            
            # 等待30秒后更新
            try:
                time.sleep(30)
            except KeyboardInterrupt:
                print("\n👋 多时间周期分析已退出")
                break
                
    except KeyboardInterrupt:
        print("\n👋 多时间周期分析已退出")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

if __name__ == "__main__":
    main()
