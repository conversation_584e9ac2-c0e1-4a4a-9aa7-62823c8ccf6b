#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OKX实盘交易启动器
"""

import time
from datetime import datetime
from okx_trading_system_optimized import OKXOptimizedTradingSystem

def main():
    """启动实盘交易"""
    print("🚀" * 50)
    print("                OKX实盘交易系统启动")
    print("🚀" * 50)
    print()
    
    try:
        # 初始化系统
        print("🔧 正在初始化交易系统...")
        system = OKXOptimizedTradingSystem()
        
        # 系统检查
        print("🔍 正在进行系统检查...")
        
        # 检查账户余额
        try:
            balance = system.get_account_balance()
            if balance > 0:
                print(f"✅ 账户余额: {balance:.2f} USDT")
            else:
                print("⚠️ 账户余额不足或获取失败")
        except Exception as e:
            print(f"⚠️ 账户检查异常: {e}")
        
        # 检查币种列表
        try:
            symbols, _ = system.get_dynamic_symbol_ranking()
            if symbols:
                print(f"✅ 币种列表: {len(symbols)} 个活跃币种")
                print(f"🏆 TOP5: {', '.join([s.replace('-USDT-SWAP', '') for s in symbols[:5]])}")
            else:
                print("❌ 无法获取币种列表")
                return
        except Exception as e:
            print(f"❌ 币种列表检查失败: {e}")
            return
        
        # 测试信号分析
        print("🧪 测试信号分析功能...")
        test_symbol = symbols[0]
        try:
            analysis = system.analyze_signal(test_symbol)
            if analysis:
                print(f"✅ 信号分析正常: {test_symbol.replace('-USDT-SWAP', '')} - {analysis['signal']} (置信度: {analysis['confidence']}%)")
            else:
                print(f"⚠️ 信号分析异常: {test_symbol}")
        except Exception as e:
            print(f"⚠️ 信号分析测试失败: {e}")
        
        print("\n" + "=" * 100)
        print("🎯 系统检查完成，准备启动实盘交易")
        print("=" * 100)
        
        # 显示交易参数
        config = system.config
        print(f"📊 交易参数:")
        print(f"   • 最大仓位: {config.MAX_POSITION_SIZE} 张")
        print(f"   • 单笔风险: {config.RISK_PER_TRADE*100:.1f}%")
        print(f"   • 止损比例: {config.STOP_LOSS_PCT*100:.1f}%")
        print(f"   • 扫描间隔: {config.SCAN_INTERVAL} 秒")
        print(f"   • 买入阈值: {config.MIN_BUY_CONFIDENCE}%")
        print(f"   • 高质量阈值: {config.HIGH_QUALITY_THRESHOLD}%")
        print(f"   • 最大日亏损: {config.MAX_DAILY_LOSS_PCT*100:.1f}%")
        print(f"   • 最大持仓数: {config.MAX_CONCURRENT_POSITIONS}")
        
        print("\n🚨 风险提示:")
        print("   • 实盘交易有风险，请确保您了解相关风险")
        print("   • 建议先用小资金测试")
        print("   • 随时可以按 Ctrl+C 停止交易")
        
        print("\n⏰ 5秒后开始实盘交易...")
        for i in range(5, 0, -1):
            print(f"   {i}...", end=" ", flush=True)
            time.sleep(1)
        print("\n")
        
        print("🚀 启动实盘交易系统!")
        print("=" * 100)
        
        # 启动交易策略
        system.run_strategy()
        
    except KeyboardInterrupt:
        print("\n👋 用户停止实盘交易")
    except Exception as e:
        print(f"❌ 实盘启动异常: {e}")

if __name__ == "__main__":
    main()
