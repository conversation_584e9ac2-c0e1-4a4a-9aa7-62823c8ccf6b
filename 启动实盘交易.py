#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OKX实盘交易启动脚本
包含安全检查和确认机制
"""

import os
import sys
import time
from okx_trading_system_optimized import OKXOptimizedTradingSystem

def print_banner():
    """显示启动横幅"""
    print("=" * 80)
    print("🚀 OKX优化交易系统 - 实盘交易模式")
    print("=" * 80)
    print("⚠️  警告: 这是真实资金交易，请确保您已充分了解风险!")
    print("💰 建议: 首次运行请使用小额资金测试")
    print("📊 策略: 多指标金叉+OBV信号+严格风险控制")
    print("=" * 80)

def safety_check():
    """安全检查"""
    print("\n🔒 实盘交易安全检查:")
    print("=" * 50)
    
    checks = [
        "✅ API认证机制已验证",
        "✅ 风险控制参数已设置",
        "✅ 止损机制已启用", 
        "✅ 仓位管理已配置",
        "✅ OBV信号系统已就绪",
        "✅ 错误处理机制已完善"
    ]
    
    for check in checks:
        print(f"   {check}")
        time.sleep(0.3)
    
    print("\n📋 当前风险参数:")
    print(f"   💰 单笔风险: 2.5%")
    print(f"   🛡️ 止损比例: 2%") 
    print(f"   📊 信号阈值: 75%")
    print(f"   🔄 日亏损限制: 5%")
    print(f"   📈 最大持仓: 3个")
    print(f"   ⏰ 扫描间隔: 5秒")

def get_user_confirmation():
    """获取用户确认"""
    print("\n" + "=" * 50)
    print("⚠️  最终确认")
    print("=" * 50)
    
    questions = [
        "1. 您是否已阅读并理解交易风险?",
        "2. 您是否确认API密钥配置正确?", 
        "3. 您是否同意使用当前风险参数?",
        "4. 您是否准备开始实盘交易?"
    ]
    
    for question in questions:
        print(f"\n{question}")
        answer = input("   请输入 'yes' 确认: ").strip().lower()
        if answer != 'yes':
            print("❌ 用户取消交易")
            return False
    
    return True

def final_countdown():
    """最终倒计时"""
    print("\n🚀 准备启动实盘交易...")
    print("⏰ 倒计时:")
    
    for i in range(5, 0, -1):
        print(f"   {i}秒后开始交易... (Ctrl+C 取消)")
        time.sleep(1)
    
    print("✅ 启动交易系统!")

def test_connection():
    """测试连接"""
    print("\n🔗 测试API连接...")
    try:
        system = OKXOptimizedTradingSystem()
        balance = system.get_account_balance()
        if balance > 0:
            print(f"✅ 连接成功! 账户余额: {balance:.2f} USDT")
            return True
        else:
            print("❌ 无法获取账户余额")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 显示横幅
        print_banner()
        
        # 测试连接
        if not test_connection():
            print("\n❌ 请检查API配置后重试")
            return
        
        # 安全检查
        safety_check()
        
        # 用户确认
        if not get_user_confirmation():
            return
        
        # 最终倒计时
        final_countdown()
        
        # 启动交易系统
        print("\n" + "=" * 80)
        system = OKXOptimizedTradingSystem()
        system.run_strategy()
        
    except KeyboardInterrupt:
        print("\n\n👋 用户取消启动")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
