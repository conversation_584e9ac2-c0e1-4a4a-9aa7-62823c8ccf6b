#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多时间周期涨跌幅功能
"""

from okx_trading_system_optimized import OKXOptimizedTradingSystem

def test_multi_timeframe():
    """测试多时间周期功能"""
    print("🔧 初始化交易系统...")
    system = OKXOptimizedTradingSystem()
    
    # 测试币种
    test_symbols = ['BTC-USDT-SWAP', 'ETH-USDT-SWAP', 'BNB-USDT-SWAP']
    
    print("\n📊 测试多时间周期涨跌幅功能")
    print("=" * 80)
    
    for symbol in test_symbols:
        print(f"\n🔍 分析 {symbol}:")
        
        try:
            # 测试多时间周期涨跌幅计算
            changes = system.get_multi_timeframe_changes(symbol)
            
            if changes:
                print(f"   ✅ 多时间周期数据获取成功:")
                for tf, data in changes.items():
                    change_pct = data['change_pct']
                    current_price = data['current_price']
                    
                    if change_pct > 0:
                        trend_icon = "📈"
                    elif change_pct < 0:
                        trend_icon = "📉"
                    else:
                        trend_icon = "➡️"
                    
                    print(f"      {tf:>3}: {trend_icon} {change_pct:+6.2f}% (价格: {current_price:.6f})")
            else:
                print(f"   ❌ 无法获取多时间周期数据")
            
            # 测试实时价格（包含多时间周期）
            price_data = system.get_real_time_price(symbol)
            
            if price_data and 'multi_timeframe_changes' in price_data:
                print(f"   ✅ 实时价格集成多时间周期成功")
                print(f"      当前价格: {price_data['last_price']:.6f}")
                print(f"      24h涨跌: {price_data['change_24h']:+.2f}%")
                
                multi_changes = price_data['multi_timeframe_changes']
                print(f"      短期趋势:")
                for tf in ['1m', '3m', '5m', '15m']:
                    if tf in multi_changes:
                        change = multi_changes[tf]['change_pct']
                        print(f"        {tf}: {change:+.2f}%")
            else:
                print(f"   ⚠️ 实时价格未包含多时间周期数据")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
    
    print("\n" + "=" * 80)
    print("📊 测试完成")

if __name__ == "__main__":
    test_multi_timeframe()
