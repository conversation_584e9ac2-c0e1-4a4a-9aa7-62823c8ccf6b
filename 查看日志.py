#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OKX交易系统日志查看工具
简单易用的日志分析工具
"""

import os
import sys
from datetime import datetime, timedelta

def print_banner():
    """显示横幅"""
    print("=" * 80)
    print("📋 OKX交易系统日志查看器")
    print("=" * 80)

def check_log_files():
    """检查日志文件"""
    log_files = {
        'okx_trading_detailed.log': '📊 详细系统日志',
        'okx_trades.log': '💰 交易记录日志',
        'okx_signals.log': '📈 信号分析日志',
        'okx_errors.log': '❌ 错误日志'
    }
    
    available_logs = {}
    for filename, description in log_files.items():
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            size_mb = size / (1024 * 1024)
            available_logs[filename] = {
                'desc': description,
                'size': f"{size_mb:.2f}MB" if size_mb > 1 else f"{size}B"
            }
    
    return available_logs

def show_log_menu(available_logs):
    """显示日志菜单"""
    print("\n📋 可用日志文件:")
    print("-" * 50)
    
    options = list(available_logs.keys())
    for i, filename in enumerate(options, 1):
        info = available_logs[filename]
        print(f"  {i}. {info['desc']} ({info['size']})")
    
    print(f"  {len(options)+1}. 📊 今日交易总结")
    print(f"  {len(options)+2}. 🔍 实时监控日志")
    print(f"  {len(options)+3}. ❌ 最近错误")
    print("  0. 退出")
    
    return options

def read_log_tail(filename, lines=50):
    """读取日志文件末尾"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            all_lines = f.readlines()
            return all_lines[-lines:] if len(all_lines) > lines else all_lines
    except Exception as e:
        return [f"读取日志失败: {e}"]

def read_log_today(filename):
    """读取今日日志"""
    today = datetime.now().strftime('%Y-%m-%d')
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            today_lines = [line for line in lines if today in line]
            return today_lines
    except Exception as e:
        return [f"读取今日日志失败: {e}"]

def show_trade_summary():
    """显示今日交易总结"""
    print("\n" + "=" * 60)
    print("💰 今日交易总结")
    print("=" * 60)
    
    if not os.path.exists('okx_trades.log'):
        print("❌ 交易日志文件不存在")
        return
    
    today_trades = read_log_today('okx_trades.log')
    
    if not today_trades:
        print("📝 今日暂无交易记录")
        return
    
    # 统计交易
    buy_count = sum(1 for line in today_trades if '买入' in line)
    sell_count = sum(1 for line in today_trades if '卖出' in line)
    open_count = sum(1 for line in today_trades if '开仓交易' in line)
    close_count = sum(1 for line in today_trades if '平仓交易' in line)
    
    print(f"📊 交易统计:")
    print(f"   🔥 开仓: {open_count} 笔")
    print(f"   🔄 平仓: {close_count} 笔")
    print(f"   📈 买入: {buy_count} 笔")
    print(f"   📉 卖出: {sell_count} 笔")
    
    print(f"\n📋 最近10笔交易:")
    recent_trades = today_trades[-10:] if len(today_trades) > 10 else today_trades
    for line in recent_trades:
        print(f"   {line.strip()}")

def show_recent_errors():
    """显示最近错误"""
    print("\n" + "=" * 60)
    print("❌ 最近错误日志")
    print("=" * 60)
    
    if not os.path.exists('okx_errors.log'):
        print("✅ 暂无错误日志")
        return
    
    recent_errors = read_log_tail('okx_errors.log', 20)
    
    if not recent_errors:
        print("✅ 最近无错误记录")
        return
    
    for line in recent_errors:
        print(f"   {line.strip()}")

def monitor_live_log():
    """实时监控日志"""
    print("\n" + "=" * 60)
    print("🔍 实时日志监控 (按 Ctrl+C 退出)")
    print("=" * 60)
    
    if not os.path.exists('okx_trading_detailed.log'):
        print("❌ 详细日志文件不存在")
        return
    
    try:
        import time
        
        # 获取文件当前大小
        with open('okx_trading_detailed.log', 'r', encoding='utf-8') as f:
            f.seek(0, 2)  # 移动到文件末尾
            
            print("🔍 开始监控，等待新日志...")
            
            while True:
                line = f.readline()
                if line:
                    print(f"📝 {line.strip()}")
                else:
                    time.sleep(1)
                    
    except KeyboardInterrupt:
        print("\n👋 停止监控")
    except Exception as e:
        print(f"❌ 监控失败: {e}")

def main():
    """主函数"""
    print_banner()
    
    while True:
        available_logs = check_log_files()
        
        if not available_logs:
            print("❌ 未找到任何日志文件")
            print("💡 请先运行交易系统生成日志")
            break
        
        options = show_log_menu(available_logs)
        
        try:
            choice = input(f"\n请选择 (0-{len(options)+3}): ").strip()
            
            if choice == '0':
                print("👋 退出日志查看器")
                break
            elif choice.isdigit():
                choice_num = int(choice)
                
                if 1 <= choice_num <= len(options):
                    # 查看指定日志文件
                    filename = options[choice_num - 1]
                    print(f"\n📋 {available_logs[filename]['desc']} (最近50行)")
                    print("-" * 60)
                    
                    lines = read_log_tail(filename, 50)
                    for line in lines:
                        print(line.strip())
                        
                elif choice_num == len(options) + 1:
                    # 今日交易总结
                    show_trade_summary()
                    
                elif choice_num == len(options) + 2:
                    # 实时监控
                    monitor_live_log()
                    
                elif choice_num == len(options) + 3:
                    # 最近错误
                    show_recent_errors()
                    
                else:
                    print("❌ 无效选择")
            else:
                print("❌ 请输入数字")
                
        except KeyboardInterrupt:
            print("\n👋 退出日志查看器")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
