#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OKX交易系统状态检查脚本
快速验证系统各项功能
"""

import sys
import time
from okx_trading_system_optimized import OKXOptimizedTradingSystem

def check_api_connection():
    """检查API连接"""
    print("🔗 检查API连接...")
    try:
        system = OKXOptimizedTradingSystem()
        balance = system.get_account_balance()
        if balance >= 0:  # 修改条件，允许0余额
            print(f"   ✅ API连接正常, 余额: {balance:.2f} USDT")
            if balance == 0:
                print("   ⚠️ 账户余额为0，请确保有足够资金进行交易")
            return True, system
        else:
            print("   ❌ 无法获取账户余额")
            return False, None
    except Exception as e:
        print(f"   ❌ API连接失败: {e}")
        return False, None

def check_market_data(system):
    """检查市场数据"""
    print("\n📊 检查市场数据...")
    try:
        # 测试获取K线数据
        klines = system.get_klines('BTC-USDT-SWAP', '1m', 10)
        if len(klines) >= 10:
            print(f"   ✅ K线数据正常, 获取到 {len(klines)} 条数据")
        else:
            print(f"   ⚠️ K线数据不足, 仅获取到 {len(klines)} 条")
        
        # 测试获取实时价格
        price_data = system.get_real_time_price('BTC-USDT-SWAP')
        if price_data and price_data.get('last_price', 0) > 0:
            print(f"   ✅ 实时价格正常: {price_data['last_price']:.2f}")
        else:
            print("   ❌ 无法获取实时价格")
            
        return True
    except Exception as e:
        print(f"   ❌ 市场数据异常: {e}")
        return False

def check_technical_indicators(system):
    """检查技术指标计算"""
    print("\n📈 检查技术指标...")
    try:
        analysis = system.analyze_signal('BTC-USDT-SWAP')
        if analysis:
            print(f"   ✅ 信号分析正常")
            print(f"   📊 当前信号: {analysis['signal']}")
            print(f"   🎯 置信度: {analysis['confidence']}%")
            
            # 检查OBV指标
            if analysis.get('obv'):
                obv = analysis['obv']
                if obv['ma5'] and obv['ma18']:
                    trend = "金叉" if obv['ma5'] > obv['ma18'] else "死叉"
                    print(f"   📈 OBV状态: {trend}")
                else:
                    print("   ⚠️ OBV数据不完整")
            
            return True
        else:
            print("   ❌ 无法获取信号分析")
            return False
    except Exception as e:
        print(f"   ❌ 技术指标异常: {e}")
        return False

def check_risk_controls(system):
    """检查风险控制"""
    print("\n🛡️ 检查风险控制...")
    try:
        # 测试风险检查
        risk_ok, risk_msg = system.check_optimized_risk_limits('BTC-USDT-SWAP', 80)
        print(f"   📋 风险检查: {risk_msg}")
        
        # 检查仓位计算
        balance = system.get_account_balance()
        position_size = system.calculate_dynamic_position_size(80, balance)
        print(f"   💰 动态仓位: {position_size} 张")
        
        return True
    except Exception as e:
        print(f"   ❌ 风险控制异常: {e}")
        return False

def check_position_management(system):
    """检查持仓管理"""
    print("\n📋 检查持仓管理...")
    try:
        positions = system.get_positions()
        print(f"   📊 当前持仓数量: {len(positions)}")
        
        if positions:
            for pos in positions[:3]:  # 显示前3个持仓
                symbol = pos.get('instId', 'Unknown')
                size = pos.get('pos', '0')
                pnl = pos.get('upl', '0')
                print(f"   💼 {symbol}: {size} 张, 盈亏: {pnl}")
        else:
            print("   📝 当前无持仓")
            
        return True
    except Exception as e:
        print(f"   ❌ 持仓管理异常: {e}")
        return False

def system_health_summary(results):
    """系统健康度总结"""
    print("\n" + "=" * 60)
    print("📊 系统健康度报告")
    print("=" * 60)
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    health_score = (passed_checks / total_checks) * 100
    
    for check_name, result in results.items():
        status = "✅ 正常" if result else "❌ 异常"
        print(f"   {check_name}: {status}")
    
    print(f"\n🎯 系统健康度: {health_score:.1f}% ({passed_checks}/{total_checks})")
    
    if health_score >= 90:
        print("✅ 系统状态优秀，可以安全运行实盘交易")
    elif health_score >= 70:
        print("⚠️ 系统状态良好，建议检查异常项后运行")
    else:
        print("❌ 系统状态异常，请修复问题后再运行实盘")
    
    return health_score

def main():
    """主函数"""
    print("🔍 OKX交易系统状态检查")
    print("=" * 60)
    
    results = {}
    
    # API连接检查
    api_ok, system = check_api_connection()
    results["API连接"] = api_ok
    
    if not api_ok:
        print("\n❌ API连接失败，无法继续检查")
        return
    
    # 市场数据检查
    results["市场数据"] = check_market_data(system)
    
    # 技术指标检查
    results["技术指标"] = check_technical_indicators(system)
    
    # 风险控制检查
    results["风险控制"] = check_risk_controls(system)
    
    # 持仓管理检查
    results["持仓管理"] = check_position_management(system)
    
    # 生成健康度报告
    health_score = system_health_summary(results)
    
    print(f"\n⏰ 检查完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
