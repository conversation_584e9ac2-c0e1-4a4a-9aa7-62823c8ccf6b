#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OKX交易系统主控制台 - 统一入口
集成所有功能：实盘交易、走势分析、排行榜、多时间周期分析
"""

import time
import os
import sys
import logging
from datetime import datetime
from okx_trading_system_optimized import OKXOptimizedTradingSystem

def setup_console_logging():
    """设置控制台日志"""
    formatter = logging.Formatter(
        '%(asctime)s | %(levelname)-5s | CONSOLE | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 使用统一日志文件
    handler = logging.FileHandler('okx_trading_unified.log', encoding='utf-8')
    handler.setLevel(logging.INFO)
    handler.setFormatter(formatter)

    logger = logging.getLogger('CONSOLE')
    logger.setLevel(logging.INFO)
    logger.addHandler(handler)
    logger.propagate = False

    return logger

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_main_menu():
    """显示主菜单"""
    clear_screen()
    print("🚀" * 60)
    print("                    OKX交易系统主控制台")
    print("🚀" * 60)
    print()
    print("选择运行模式:")
    print()
    print("💰 交易模式:")
    print("  1. 🎯 启动实盘交易 (自动交易)")
    print("  2. 🧪 信号测试模式")
    print()
    print("📊 分析模式:")
    print("  3. 📈 自动走势监控 (推荐)")
    print("  4. 🏆 动态排行榜")
    print("  5. ⏱️ 多时间周期分析")
    print("  6. 📊 市场趋势统计")
    print()
    print("🔧 工具模式:")
    print("  7. 🔍 数据准确性检查")
    print("  8. 📋 系统状态检查")
    print("  9. 📊 技术指标状态")
    print()
    print("  0. 退出")
    print()

def format_change_display(change_pct):
    """格式化涨跌幅显示"""
    if change_pct > 5:
        return f"🚀 +{change_pct:.2f}%"
    elif change_pct > 2:
        return f"📈 +{change_pct:.2f}%"
    elif change_pct > 0:
        return f"🟢 +{change_pct:.2f}%"
    elif change_pct > -2:
        return f"🔴 {change_pct:.2f}%"
    elif change_pct > -5:
        return f"📉 {change_pct:.2f}%"
    else:
        return f"💥 {change_pct:.2f}%"

def start_live_trading(system):
    """启动实盘交易"""
    clear_screen()
    print("🎯 实盘交易启动器")
    print("=" * 80)
    
    # 系统检查
    print("🔍 系统检查中...")
    
    try:
        # 检查账户
        balance = system.get_account_balance()
        print(f"💰 账户余额: {balance:.2f} USDT")
        
        # 检查币种
        symbols, _ = system.get_dynamic_symbol_ranking()
        print(f"📊 活跃币种: {len(symbols)} 个")
        
        # 显示交易参数
        config = system.config
        print(f"\n📋 交易参数:")
        print(f"   • 单笔风险: {config.RISK_PER_TRADE*100:.1f}%")
        print(f"   • 买入阈值: {config.MIN_BUY_CONFIDENCE}%")
        print(f"   • 扫描间隔: {config.SCAN_INTERVAL}秒")
        
        print(f"\n🚨 风险提示: 实盘交易有风险，请谨慎操作")
        
        confirm = input("\n确认启动实盘交易? (y/N): ").strip().lower()
        if confirm == 'y':
            print("\n🚀 启动实盘交易系统...")
            system.run_strategy()
        else:
            print("❌ 取消启动")
            
    except Exception as e:
        print(f"❌ 系统检查失败: {e}")
    
    input("\n按回车键返回主菜单...")

def signal_test_mode(system):
    """详细指标分析模式"""
    clear_screen()
    print("🧪 详细技术指标分析")
    print("=" * 120)

    try:
        symbols, _ = system.get_dynamic_symbol_ranking()

        print(f"🔍 分析前5个币种的详细指标状态...")
        print("-" * 120)

        for i, symbol in enumerate(symbols[:5], 1):
            try:
                analysis = system.analyze_signal(symbol)
                price_data = system.get_real_time_price(symbol)

                if analysis and price_data:
                    symbol_short = symbol.replace('-USDT-SWAP', '')
                    signal = analysis['signal']
                    confidence = analysis['confidence']
                    price = price_data['last_price']
                    change_24h = price_data['change_24h']

                    print(f"\n📊 {i}. {symbol_short} - 当前价格: {price:.6f} | 24h涨跌: {format_change_display(change_24h)}")
                    print("-" * 120)

                    # 信号总览
                    signal_display = "🟢买入" if signal == "BUY" else "🔴卖出" if signal == "SELL" else "⚪持有"
                    print(f"🎯 交易信号: {signal_display} | 置信度: {confidence}% | 金叉数量: {analysis.get('golden_cross_count', 0)}")

                    # 具体金叉信号
                    cross_signals = analysis.get('cross_signals', [])
                    if cross_signals:
                        print(f"⚡ 金叉信号: {', '.join(cross_signals)}")

                    # 技术指标详情
                    rsi = analysis.get('rsi', 0)
                    kdj = analysis.get('kdj', {})
                    obv = analysis.get('obv', {})
                    ma50_diff = analysis.get('ma50_diff', 0)

                    print(f"📈 技术指标:")
                    print(f"   • RSI: {rsi:.1f} {'🔥超买' if rsi > 70 else '💎超卖' if rsi < 30 else '🟡中性'}")

                    if kdj.get('k') and kdj.get('d'):
                        k_val = kdj['k']
                        d_val = kdj['d']
                        j_val = kdj.get('j', 0)
                        kdj_status = "🟢金叉" if k_val > d_val else "🔴死叉"
                        print(f"   • KDJ: K={k_val:.1f} D={d_val:.1f} J={j_val:.1f} {kdj_status}")

                    if obv.get('ma5') and obv.get('ma18'):
                        obv_ma5 = obv['ma5']
                        obv_ma18 = obv['ma18']
                        obv_status = "🟢金叉" if obv_ma5 > obv_ma18 else "🔴死叉"
                        obv_strength = abs((obv_ma5 - obv_ma18) / obv_ma18 * 100) if obv_ma18 != 0 else 0
                        print(f"   • OBV: MA5/MA18 {obv_status} | 强度: {obv_strength:.1f}%")

                    print(f"   • MA50偏离: {ma50_diff:+.2f}% {'🚀突破' if ma50_diff > 0 else '📉下破' if ma50_diff < -2 else '➡️接近'}")

                    # 形态和信号
                    patterns = analysis.get('patterns', [])
                    if patterns:
                        print(f"🔍 K线形态: {', '.join(patterns)}")

                    signals = analysis.get('signals', [])
                    if signals:
                        print(f"📋 信号列表: {', '.join(signals[:3])}{'...' if len(signals) > 3 else ''}")

            except Exception as e:
                print(f"❌ {symbol} 分析失败: {e}")
                continue

        print("\n" + "=" * 120)

    except Exception as e:
        print(f"❌ 测试失败: {e}")

    input("\n按回车键返回主菜单...")

def auto_trend_monitor(system):
    """自动走势监控"""
    clear_screen()
    print("📈 启动自动走势监控...")
    print("💡 功能循环: 实时监控 → 排行榜 → 多时间周期 → 趋势统计")
    print("⏰ 每个功能显示15秒，按 Ctrl+C 返回菜单")
    time.sleep(3)
    
    cycle_count = 0
    
    try:
        while True:
            cycle_count += 1
            
            if cycle_count % 4 == 1:
                show_real_time_monitor(system)
            elif cycle_count % 4 == 2:
                show_ranking_board(system)
            elif cycle_count % 4 == 3:
                show_timeframe_analysis(system)
            else:
                show_trend_statistics(system)
            
            print(f"\n⏰ 15秒后自动切换... (周期 {cycle_count}) | 按 Ctrl+C 返回菜单")
            time.sleep(15)
            
    except KeyboardInterrupt:
        print("\n👋 返回主菜单")

def show_real_time_monitor(system):
    """显示实时监控"""
    clear_screen()
    print("📊 OKX实时涨跌排行榜")
    print("=" * 100)
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')} | 🔄 每20秒更新")

    try:
        symbols, ranking_data = system.get_dynamic_symbol_ranking()

        print(f"\n🏆 TOP20 今日涨跌排行 (共{len(symbols)}个币种)")
        print("-" * 100)
        print("排名 | 币种     | 价格        | 24h涨跌   | 1M   | 5M   | 15M  | 成交量   | 趋势   | 信号")
        print("-" * 100)
        
        for i, symbol in enumerate(symbols[:20], 1):
            try:
                # 获取实时数据
                price_data = system.get_real_time_price(symbol)
                if not price_data:
                    continue

                # 获取信号分析
                analysis = system.analyze_signal(symbol)

                symbol_short = symbol.replace('-USDT-SWAP', '')
                current_price = price_data['last_price']
                change_24h = price_data['change_24h']
                vol_24h = price_data['vol_24h'] / 10000

                multi_changes = price_data.get('multi_timeframe_changes', {})

                # 简化显示格式
                change_24h_display = format_change_display(change_24h)

                # 短期涨跌 (简化)
                def simple_format(pct):
                    if pct > 0.5:
                        return f"🟢{pct:+.1f}"
                    elif pct < -0.5:
                        return f"🔴{pct:.1f}"
                    else:
                        return f"⚪0.0"

                change_1m = simple_format(multi_changes.get('1m', {}).get('change_pct', 0))
                change_5m = simple_format(multi_changes.get('5m', {}).get('change_pct', 0))
                change_15m = simple_format(multi_changes.get('15m', {}).get('change_pct', 0))

                # 趋势判断 (简化)
                changes = [multi_changes.get(tf, {}).get('change_pct', 0) for tf in ['1m', '5m', '15m']]
                positive_count = sum(1 for c in changes if c > 0)

                if positive_count >= 2:
                    trend = "🚀上涨"
                elif positive_count == 1:
                    trend = "➡️震荡"
                else:
                    trend = "📉下跌"

                # 成交量 (简化)
                if vol_24h > 5000:
                    vol_display = f"🔥{vol_24h:.0f}万"
                elif vol_24h > 1000:
                    vol_display = f"🟡{vol_24h:.0f}万"
                else:
                    vol_display = f"🔵{vol_24h:.0f}万"

                # 交易信号 (简化)
                if analysis:
                    signal = analysis['signal']
                    confidence = analysis['confidence']
                    if signal == 'BUY' and confidence >= 85:
                        signal_display = "🟢买入"
                    elif signal == 'SELL' and confidence >= 85:
                        signal_display = "🔴卖出"
                    else:
                        signal_display = "⚪观望"
                else:
                    signal_display = "❓无"

                # 价格显示 (简化)
                if current_price >= 1:
                    price_display = f"{current_price:.3f}"
                else:
                    price_display = f"{current_price:.6f}"

                print(f"{i:>2} | {symbol_short:<8} | {price_display:>10} | {change_24h_display:<9} | {change_1m:<4} | {change_5m:<4} | {change_15m:<4} | {vol_display:<8} | {trend:<6} | {signal_display}")

            except Exception as e:
                continue
                
    except Exception as e:
        print(f"❌ 监控异常: {e}")

def show_ranking_board(system):
    """显示排行榜"""
    clear_screen()
    print("🏆 OKX涨跌排行榜")
    print("=" * 80)
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')} | 📊 智能综合排序")

    try:
        symbols, ranking_data = system.get_dynamic_symbol_ranking()

        if ranking_data:
            print(f"\n📈 TOP20 综合排行榜 (共{len(symbols)}个币种)")
            print("-" * 80)
            print("排名 | 币种     | 价格      | 24h涨跌   | 成交量   | 评分 | 推荐")
            print("-" * 80)

            for i, ticker in enumerate(ranking_data[:20], 1):
                symbol = ticker['instId'].replace('-USDT-SWAP', '')
                price = float(ticker['last'])
                change_24h = float(ticker.get('chg24h', ticker.get('changePercent24h', ticker.get('priceChangePercent', 0))))
                vol_24h = float(ticker['vol24h']) / 10000
                score = ticker['composite_score']

                change_display = format_change_display(change_24h)

                # 简化推荐度
                if score >= 70:
                    recommend = "⭐⭐⭐⭐⭐"
                elif score >= 50:
                    recommend = "⭐⭐⭐⭐"
                elif score >= 30:
                    recommend = "⭐⭐⭐"
                else:
                    recommend = "⭐⭐"

                # 简化价格显示
                if price >= 1:
                    price_display = f"{price:.3f}"
                else:
                    price_display = f"{price:.6f}"

                # 简化成交量显示
                if vol_24h >= 10000:
                    vol_display = f"{vol_24h/10000:.1f}亿"
                elif vol_24h >= 1000:
                    vol_display = f"{vol_24h/1000:.1f}千万"
                else:
                    vol_display = f"{vol_24h:.0f}万"

                print(f"{i:>2} | {symbol:<8} | {price_display:>9} | {change_display:<9} | {vol_display:<8} | {score:>4.0f} | {recommend}")

    except Exception as e:
        print(f"❌ 排行榜异常: {e}")

def show_timeframe_analysis(system):
    """显示多时间周期分析"""
    clear_screen()
    print("⏱️ 多时间周期涨跌分析")
    print("=" * 85)
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')} | 📊 短期趋势分析")

    try:
        symbols, _ = system.get_dynamic_symbol_ranking()

        print(f"\n📊 TOP15 短期涨跌趋势")
        print("-" * 85)
        print("排名 | 币种     | 价格      | 24h涨跌  | 1M   | 5M   | 15M  | 趋势   | 强度")
        print("-" * 85)
        
        for i, symbol in enumerate(symbols[:15], 1):
            try:
                price_data = system.get_real_time_price(symbol)
                if not price_data:
                    continue

                symbol_short = symbol.replace('-USDT-SWAP', '')
                current_price = price_data['last_price']
                change_24h = price_data['change_24h']
                multi_changes = price_data.get('multi_timeframe_changes', {})

                # 简化显示
                change_24h_display = format_change_display(change_24h)

                # 短期变化 (简化)
                change_1m = multi_changes.get('1m', {}).get('change_pct', 0)
                change_5m = multi_changes.get('5m', {}).get('change_pct', 0)
                change_15m = multi_changes.get('15m', {}).get('change_pct', 0)

                def mini_format(pct):
                    if pct > 0.5:
                        return f"🟢{pct:+.1f}"
                    elif pct < -0.5:
                        return f"🔴{pct:.1f}"
                    else:
                        return "⚪0.0"

                change_1m_display = mini_format(change_1m)
                change_5m_display = mini_format(change_5m)
                change_15m_display = mini_format(change_15m)

                # 趋势强度分析
                changes = [change_1m, change_5m, change_15m]
                positive_count = sum(1 for c in changes if c > 0)
                avg_change = sum(abs(c) for c in changes) / len(changes)

                if positive_count >= 2:
                    trend = "🚀上涨"
                elif positive_count == 1:
                    trend = "➡️震荡"
                else:
                    trend = "📉下跌"

                # 强度评估
                if avg_change > 2:
                    strength = "💪强"
                elif avg_change > 1:
                    strength = "🟡中"
                else:
                    strength = "🔵弱"

                # 价格显示
                if current_price >= 1:
                    price_display = f"{current_price:.3f}"
                else:
                    price_display = f"{current_price:.6f}"

                print(f"{i:>2} | {symbol_short:<8} | {price_display:>9} | {change_24h_display:<8} | {change_1m_display:<4} | {change_5m_display:<4} | {change_15m_display:<4} | {trend:<6} | {strength}")

            except Exception as e:
                continue
                
    except Exception as e:
        print(f"❌ 分析异常: {e}")

def show_trend_statistics(system):
    """显示趋势统计"""
    clear_screen()
    print("📊 市场整体趋势")
    print("=" * 70)
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')} | 🎯 市场情绪分析")

    try:
        symbols, _ = system.get_dynamic_symbol_ranking()

        # 简化趋势统计
        trend_stats = {"强势上涨": 0, "上涨": 0, "震荡": 0, "下跌": 0, "强势下跌": 0}
        signal_stats = {"买入信号": 0, "卖出信号": 0, "观望": 0}
        total_analyzed = 0
        
        for symbol in symbols[:30]:
            try:
                price_data = system.get_real_time_price(symbol)
                analysis = system.analyze_signal(symbol)

                if not price_data:
                    continue

                change_24h = price_data['change_24h']
                multi_changes = price_data.get('multi_timeframe_changes', {})

                # 简化趋势判断
                short_changes = [multi_changes.get(tf, {}).get('change_pct', 0) for tf in ['1m', '5m', '15m']]
                positive_short = sum(1 for c in short_changes if c > 0)

                if change_24h > 5 and positive_short >= 2:
                    trend_stats["强势上涨"] += 1
                elif change_24h > 0 and positive_short >= 1:
                    trend_stats["上涨"] += 1
                elif change_24h < -5 and positive_short <= 1:
                    trend_stats["强势下跌"] += 1
                elif change_24h < 0 and positive_short <= 1:
                    trend_stats["下跌"] += 1
                else:
                    trend_stats["震荡"] += 1

                # 信号统计
                if analysis:
                    signal = analysis['signal']
                    confidence = analysis['confidence']
                    if signal == 'BUY' and confidence >= 85:
                        signal_stats["买入信号"] += 1
                    elif signal == 'SELL' and confidence >= 85:
                        signal_stats["卖出信号"] += 1
                    else:
                        signal_stats["观望"] += 1
                else:
                    signal_stats["观望"] += 1

                total_analyzed += 1

            except Exception as e:
                continue

        print(f"\n📊 市场趋势分布 (分析{total_analyzed}个币种)")
        print("-" * 70)

        for trend, count in trend_stats.items():
            percentage = (count / total_analyzed * 100) if total_analyzed > 0 else 0
            bar_length = int(percentage / 3)
            bar = "█" * bar_length + "░" * (20 - bar_length)

            print(f"{trend:<6} | {bar:<20} | {count:>2}个 ({percentage:>4.0f}%)")

        print(f"\n🎯 交易信号分布")
        print("-" * 70)

        for signal, count in signal_stats.items():
            percentage = (count / total_analyzed * 100) if total_analyzed > 0 else 0
            bar_length = int(percentage / 3)
            bar = "█" * bar_length + "░" * (20 - bar_length)

            print(f"{signal:<6} | {bar:<20} | {count:>2}个 ({percentage:>4.0f}%)")

        # 市场总体判断
        up_count = trend_stats["强势上涨"] + trend_stats["上涨"]
        down_count = trend_stats["强势下跌"] + trend_stats["下跌"]
        buy_signals = signal_stats["买入信号"]

        if up_count > down_count * 1.5 and buy_signals > 3:
            market_trend = "🚀 市场强势 - 适合做多"
        elif down_count > up_count * 1.5:
            market_trend = "📉 市场弱势 - 谨慎操作"
        else:
            market_trend = "➡️ 市场震荡 - 等待机会"

        print(f"\n🎯 市场总结: {market_trend}")

    except Exception as e:
        print(f"❌ 统计异常: {e}")

def data_accuracy_check(system):
    """数据准确性检查"""
    clear_screen()
    print("🔍 数据准确性检查")
    print("=" * 60)
    
    try:
        symbols, _ = system.get_dynamic_symbol_ranking()
        print(f"✅ 币种列表: {len(symbols)} 个")
        
        # 测试前5个币种
        success_count = 0
        test_symbols = symbols[:5]
        
        for symbol in test_symbols:
            try:
                # 测试K线数据
                klines = system.get_klines(symbol, '1m', 10)
                if len(klines) >= 8:
                    success_count += 1
                    print(f"✅ {symbol.replace('-USDT-SWAP', '')}: K线数据正常 ({len(klines)}条)")
                else:
                    print(f"⚠️ {symbol.replace('-USDT-SWAP', '')}: K线数据不足")
                
                # 测试实时价格
                price_data = system.get_real_time_price(symbol)
                if price_data and price_data['last_price'] > 0:
                    print(f"✅ {symbol.replace('-USDT-SWAP', '')}: 实时价格正常")
                else:
                    print(f"❌ {symbol.replace('-USDT-SWAP', '')}: 实时价格异常")
                    
            except Exception as e:
                print(f"❌ {symbol.replace('-USDT-SWAP', '')}: 检查失败")
        
        accuracy = (success_count / len(test_symbols)) * 100
        print(f"\n📊 数据准确性: {accuracy:.1f}%")
        
        if accuracy >= 80:
            print("✅ 数据质量良好")
        else:
            print("⚠️ 数据质量需要关注")
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    
    input("\n按回车键返回主菜单...")

def system_status_check(system):
    """系统状态检查"""
    clear_screen()
    print("📋 系统状态检查")
    print("=" * 60)
    
    try:
        # 账户检查
        try:
            balance = system.get_account_balance()
            print(f"💰 账户余额: {balance:.2f} USDT")
        except Exception as e:
            print(f"⚠️ 账户检查异常: {e}")
        
        # 币种检查
        try:
            symbols, _ = system.get_dynamic_symbol_ranking()
            print(f"📊 活跃币种: {len(symbols)} 个")
        except Exception as e:
            print(f"❌ 币种检查失败: {e}")
        
        # 配置检查
        config = system.config
        print(f"\n⚙️ 交易配置:")
        print(f"   • 单笔风险: {config.RISK_PER_TRADE*100:.1f}%")
        print(f"   • 买入阈值: {config.MIN_BUY_CONFIDENCE}%")
        print(f"   • 扫描间隔: {config.SCAN_INTERVAL}秒")
        print(f"   • 最大持仓: {config.MAX_CONCURRENT_POSITIONS}")
        
        print(f"\n✅ 系统状态正常")
        
    except Exception as e:
        print(f"❌ 系统检查失败: {e}")
    
    input("\n按回车键返回主菜单...")

def auto_main_control():
    """自动主控制台 - 智能模式选择"""
    try:
        # 设置控制台日志
        console_logger = setup_console_logging()

        print("🚀 启动OKX自动交易控制台...")
        console_logger.info("🚀 启动OKX自动交易控制台")

        system = OKXOptimizedTradingSystem()
        print("✅ 系统初始化完成")
        console_logger.info("✅ 系统初始化完成")

        # 检查系统状态
        print("🔍 系统状态检查...")
        try:
            balance = system.get_account_balance()
            symbols, _ = system.get_dynamic_symbol_ranking()

            print(f"💰 账户余额: {balance:.2f} USDT")
            print(f"📊 活跃币种: {len(symbols)} 个")

            # 根据账户余额自动选择模式
            if balance > 10:  # 有余额，启动实盘交易
                print("💰 检测到账户有余额，启动实盘交易模式")
                console_logger.info(f"💰 检测到账户余额 {balance:.2f} USDT，启动实盘交易模式")
                print("🎯 3秒后开始自动实盘交易...")
                time.sleep(3)

                # 启动实盘交易
                print("🚀 启动实盘交易系统!")
                console_logger.info("🚀 启动实盘交易系统")
                system.run_strategy()

            else:  # 无余额，启动分析模式
                print("📊 账户余额不足，启动自动分析模式")
                console_logger.info(f"📊 账户余额 {balance:.2f} USDT，启动自动分析模式")
                print("📈 3秒后开始自动走势分析...")
                time.sleep(3)

                # 启动自动分析
                console_logger.info("📈 启动自动走势分析模式")
                auto_analysis_mode(system, console_logger)

        except Exception as e:
            print(f"⚠️ 状态检查异常: {e}")
            console_logger.error(f"⚠️ 状态检查异常: {e}")
            print("📊 启动默认分析模式...")
            time.sleep(2)
            auto_analysis_mode(system, console_logger)

    except KeyboardInterrupt:
        print("\n👋 用户停止程序")
        console_logger.info("👋 用户手动停止程序")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        console_logger.error(f"❌ 程序异常: {e}")

def auto_analysis_mode(system, console_logger=None):
    """自动分析模式"""
    if not console_logger:
        console_logger = setup_console_logging()

    print("📊 自动分析模式启动")
    console_logger.info("📊 自动分析模式启动")
    print("🔄 功能循环: 实时监控 → 排行榜 → 多时间周期 → 趋势统计")
    print("⏰ 每个功能显示20秒，按 Ctrl+C 可退出")

    cycle_count = 0

    try:
        while True:
            cycle_count += 1

            # 循环显示不同功能
            if cycle_count % 4 == 1:
                print(f"\n🔄 第{cycle_count}轮 - 实时走势监控")
                console_logger.info(f"🔄 第{cycle_count}轮 - 实时走势监控")
                show_real_time_monitor(system)
            elif cycle_count % 4 == 2:
                print(f"\n🔄 第{cycle_count}轮 - 动态排行榜")
                console_logger.info(f"🔄 第{cycle_count}轮 - 动态排行榜")
                show_ranking_board(system)
            elif cycle_count % 4 == 3:
                print(f"\n🔄 第{cycle_count}轮 - 多时间周期分析")
                console_logger.info(f"🔄 第{cycle_count}轮 - 多时间周期分析")
                show_timeframe_analysis(system)
            else:
                print(f"\n🔄 第{cycle_count}轮 - 市场趋势统计")
                console_logger.info(f"🔄 第{cycle_count}轮 - 市场趋势统计")
                show_trend_statistics(system)

            print(f"\n⏰ 20秒后自动切换到下一功能... | 按 Ctrl+C 退出")
            time.sleep(20)

    except KeyboardInterrupt:
        print("\n👋 自动分析已停止")
        console_logger.info("👋 自动分析已停止")

def main():
    """主函数 - 自动模式"""
    # 检查命令行参数
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'trading':
            # 强制实盘交易模式
            print("🎯 强制启动实盘交易模式")
            system = OKXOptimizedTradingSystem()
            system.run_strategy()
            return
        elif mode == 'analysis':
            # 强制分析模式
            print("📊 强制启动分析模式")
            system = OKXOptimizedTradingSystem()
            auto_analysis_mode(system)
            return
        elif mode == 'menu':
            # 手动菜单模式
            manual_menu_mode()
            return

    # 默认自动模式
    auto_main_control()

def manual_menu_mode():
    """手动菜单模式"""
    try:
        print("🔧 初始化OKX交易系统...")
        system = OKXOptimizedTradingSystem()
        print("✅ 系统初始化完成")
        time.sleep(1)

        while True:
            print_main_menu()
            choice = input("请选择功能 (0-9): ").strip()

            if choice == '1':
                start_live_trading(system)
            elif choice == '2':
                signal_test_mode(system)
            elif choice == '3':
                auto_trend_monitor(system)
            elif choice == '4':
                show_ranking_board(system)
                input("\n按回车键返回主菜单...")
            elif choice == '5':
                show_timeframe_analysis(system)
                input("\n按回车键返回主菜单...")
            elif choice == '6':
                show_trend_statistics(system)
                input("\n按回车键返回主菜单...")
            elif choice == '7':
                data_accuracy_check(system)
            elif choice == '8':
                system_status_check(system)
            elif choice == '9':
                show_indicators_status(system)
            elif choice == '0':
                print("👋 再见！")
                break
            else:
                print("❌ 无效选择")
                time.sleep(1)

    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序异常: {e}")

def show_indicators_status(system):
    """显示所有指标状态总览"""
    clear_screen()
    print("📊 技术指标状态总览")
    print("=" * 100)
    print(f"⏰ {datetime.now().strftime('%H:%M:%S')} | 🎯 多指标综合分析")

    try:
        symbols, _ = system.get_dynamic_symbol_ranking()

        # 指标统计
        indicator_stats = {
            'ema_golden': 0, 'rsi_oversold': 0, 'rsi_overbought': 0,
            'kdj_golden': 0, 'obv_golden': 0, 'ma50_above': 0,
            'total_golden_cross': 0, 'strong_signals': 0
        }

        print(f"\n🔍 分析TOP20币种指标状态...")
        print("-" * 100)
        print("币种     | 价格      | RSI  | KDJ状态 | OBV状态 | MA50  | 金叉数 | 信号强度")
        print("-" * 100)

        for i, symbol in enumerate(symbols[:20], 1):
            try:
                analysis = system.analyze_signal(symbol)
                price_data = system.get_real_time_price(symbol)

                if not analysis or not price_data:
                    continue

                symbol_short = symbol.replace('-USDT-SWAP', '')
                price = price_data['last_price']

                # RSI状态
                rsi = analysis.get('rsi', 50)
                if rsi > 70:
                    rsi_status = "🔥超买"
                    indicator_stats['rsi_overbought'] += 1
                elif rsi < 30:
                    rsi_status = "💎超卖"
                    indicator_stats['rsi_oversold'] += 1
                else:
                    rsi_status = f"{rsi:.0f}"

                # KDJ状态
                kdj = analysis.get('kdj', {})
                if kdj.get('k') and kdj.get('d'):
                    if kdj['k'] > kdj['d']:
                        kdj_status = "🟢金叉"
                        indicator_stats['kdj_golden'] += 1
                    else:
                        kdj_status = "🔴死叉"
                else:
                    kdj_status = "❓无"

                # OBV状态
                obv = analysis.get('obv', {})
                if obv.get('ma5') and obv.get('ma18'):
                    if obv['ma5'] > obv['ma18']:
                        obv_status = "🟢金叉"
                        indicator_stats['obv_golden'] += 1
                    else:
                        obv_status = "🔴死叉"
                else:
                    obv_status = "❓无"

                # MA50状态
                ma50_diff = analysis.get('ma50_diff', 0)
                if ma50_diff > 0:
                    ma50_status = f"+{ma50_diff:.1f}%"
                    indicator_stats['ma50_above'] += 1
                else:
                    ma50_status = f"{ma50_diff:.1f}%"

                # 金叉数量
                golden_cross_count = analysis.get('golden_cross_count', 0)
                indicator_stats['total_golden_cross'] += golden_cross_count

                # 信号强度
                confidence = analysis.get('confidence', 0)
                if confidence >= 80:
                    strength = "🚀强"
                    indicator_stats['strong_signals'] += 1
                elif confidence >= 60:
                    strength = "📈中"
                else:
                    strength = "⚪弱"

                # 价格显示
                if price >= 1:
                    price_display = f"{price:.3f}"
                else:
                    price_display = f"{price:.6f}"

                print(f"{symbol_short:<8} | {price_display:>9} | {rsi_status:<4} | {kdj_status:<7} | {obv_status:<7} | {ma50_status:<5} | {golden_cross_count:>4} | {strength}")

            except Exception as e:
                continue

        # 显示统计结果
        print("-" * 100)
        print(f"\n📈 指标统计总览 (TOP20币种)")
        print("-" * 50)
        print(f"🟢 KDJ金叉: {indicator_stats['kdj_golden']} 个")
        print(f"🟢 OBV金叉: {indicator_stats['obv_golden']} 个")
        print(f"📈 MA50上方: {indicator_stats['ma50_above']} 个")
        print(f"💎 RSI超卖: {indicator_stats['rsi_oversold']} 个")
        print(f"🔥 RSI超买: {indicator_stats['rsi_overbought']} 个")
        print(f"⚡ 总金叉数: {indicator_stats['total_golden_cross']} 次")
        print(f"🚀 强信号: {indicator_stats['strong_signals']} 个")

        # 市场判断
        golden_ratio = (indicator_stats['kdj_golden'] + indicator_stats['obv_golden']) / 40 * 100
        if golden_ratio > 60:
            market_status = "🚀 多头市场 - 金叉信号密集"
        elif golden_ratio > 40:
            market_status = "📈 偏多市场 - 适度看多"
        elif golden_ratio < 20:
            market_status = "📉 偏空市场 - 谨慎操作"
        else:
            market_status = "➡️ 震荡市场 - 等待机会"

        print(f"\n🎯 市场判断: {market_status}")
        print(f"📊 金叉比例: {golden_ratio:.1f}%")

    except Exception as e:
        print(f"❌ 分析失败: {e}")

    input("\n按回车键返回主菜单...")

if __name__ == "__main__":
    main()
